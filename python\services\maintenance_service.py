"""
Maintenance service for managing bot maintenance states and user access control
Handles general maintenance, withdrawal maintenance, and maintenance message delivery
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from config.database import get_collection, COLLECTIONS
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class MaintenanceService:
    """Service for bot maintenance management"""
    
    async def get_maintenance_status(self) -> Dict[str, Any]:
        """Get current maintenance status for all systems"""
        try:
            admin_settings_collection = await get_collection(COLLECTIONS['admin_settings'])
            
            # Get maintenance settings
            maintenance_settings = await admin_settings_collection.find_one({"setting_type": "maintenance"})
            
            if not maintenance_settings:
                # Create default maintenance settings
                default_settings = {
                    "setting_type": "maintenance",
                    "general_maintenance": False,
                    "bank_withdrawal_maintenance": False,
                    "usdt_withdrawal_maintenance": False,
                    "created_at": get_current_timestamp(),
                    "updated_at": get_current_timestamp()
                }
                await admin_settings_collection.insert_one(default_settings)
                return {
                    "general_maintenance": False,
                    "bank_withdrawal_maintenance": False,
                    "usdt_withdrawal_maintenance": False
                }
            
            return {
                "general_maintenance": maintenance_settings.get("general_maintenance", False),
                "bank_withdrawal_maintenance": maintenance_settings.get("bank_withdrawal_maintenance", False),
                "usdt_withdrawal_maintenance": maintenance_settings.get("usdt_withdrawal_maintenance", False)
            }
            
        except Exception as e:
            logger.error(f"Error getting maintenance status: {e}")
            return {
                "general_maintenance": False,
                "bank_withdrawal_maintenance": False,
                "usdt_withdrawal_maintenance": False
            }
    
    async def toggle_general_maintenance(self, admin_user_id: int) -> Tuple[bool, str]:
        """Toggle general maintenance mode"""
        try:
            admin_settings_collection = await get_collection(COLLECTIONS['admin_settings'])
            
            # Get current status
            current_status = await self.get_maintenance_status()
            new_status = not current_status["general_maintenance"]
            
            # Update maintenance status
            await admin_settings_collection.update_one(
                {"setting_type": "maintenance"},
                {
                    "$set": {
                        "general_maintenance": new_status,
                        "updated_at": get_current_timestamp(),
                        "updated_by": admin_user_id
                    }
                },
                upsert=True
            )
            
            status_text = "ON" if new_status else "OFF"
            return True, f"✅ General maintenance turned {status_text}"
            
        except Exception as e:
            logger.error(f"Error toggling general maintenance: {e}")
            return False, "❌ Failed to toggle general maintenance"
    
    async def toggle_bank_withdrawal_maintenance(self, admin_user_id: int) -> Tuple[bool, str]:
        """Toggle bank withdrawal maintenance mode"""
        try:
            admin_settings_collection = await get_collection(COLLECTIONS['admin_settings'])
            
            # Get current status
            current_status = await self.get_maintenance_status()
            new_status = not current_status["bank_withdrawal_maintenance"]
            
            # Update maintenance status
            await admin_settings_collection.update_one(
                {"setting_type": "maintenance"},
                {
                    "$set": {
                        "bank_withdrawal_maintenance": new_status,
                        "updated_at": get_current_timestamp(),
                        "updated_by": admin_user_id
                    }
                },
                upsert=True
            )
            
            status_text = "ON" if new_status else "OFF"
            return True, f"✅ Bank withdrawal maintenance turned {status_text}"
            
        except Exception as e:
            logger.error(f"Error toggling bank withdrawal maintenance: {e}")
            return False, "❌ Failed to toggle bank withdrawal maintenance"
    
    async def toggle_usdt_withdrawal_maintenance(self, admin_user_id: int) -> Tuple[bool, str]:
        """Toggle USDT withdrawal maintenance mode"""
        try:
            admin_settings_collection = await get_collection(COLLECTIONS['admin_settings'])
            
            # Get current status
            current_status = await self.get_maintenance_status()
            new_status = not current_status["usdt_withdrawal_maintenance"]
            
            # Update maintenance status
            await admin_settings_collection.update_one(
                {"setting_type": "maintenance"},
                {
                    "$set": {
                        "usdt_withdrawal_maintenance": new_status,
                        "updated_at": get_current_timestamp(),
                        "updated_by": admin_user_id
                    }
                },
                upsert=True
            )
            
            status_text = "ON" if new_status else "OFF"
            return True, f"✅ USDT withdrawal maintenance turned {status_text}"
            
        except Exception as e:
            logger.error(f"Error toggling USDT withdrawal maintenance: {e}")
            return False, "❌ Failed to toggle USDT withdrawal maintenance"
    
    async def is_general_maintenance_active(self) -> bool:
        """Check if general maintenance is active"""
        try:
            status = await self.get_maintenance_status()
            return status["general_maintenance"]
        except Exception as e:
            logger.error(f"Error checking general maintenance status: {e}")
            return False
    
    async def is_bank_withdrawal_maintenance_active(self) -> bool:
        """Check if bank withdrawal maintenance is active"""
        try:
            status = await self.get_maintenance_status()
            return status["bank_withdrawal_maintenance"]
        except Exception as e:
            logger.error(f"Error checking bank withdrawal maintenance status: {e}")
            return False
    
    async def is_usdt_withdrawal_maintenance_active(self) -> bool:
        """Check if USDT withdrawal maintenance is active"""
        try:
            status = await self.get_maintenance_status()
            return status["usdt_withdrawal_maintenance"]
        except Exception as e:
            logger.error(f"Error checking USDT withdrawal maintenance status: {e}")
            return False
    
    def get_general_maintenance_message(self) -> str:
        """Get general maintenance message for blocked features"""
        return (
            "🛠️ We're currently under maintenance.\n\n"
            "Please try again later. For enquiries, contact our support:\n"
            "🔗 @instantohelpbot\n\n"
            "Thank you for your patience!"
        )
    
    def get_bank_withdrawal_maintenance_message(self) -> str:
        """Get bank withdrawal maintenance message"""
        return (
            "⚠️ Bank Withdrawal Is Under Maintenance\n\n"
            "Oops! Bank withdrawals are currently under maintenance\n"
            "We'll restore it as soon as possible!\n\n"
            "💡 Tip: You can still withdraw instantly using 💲USDT"
        )
    
    def get_usdt_withdrawal_maintenance_message(self) -> str:
        """Get USDT withdrawal maintenance message"""
        return (
            "⚠️ USDT Withdrawal Is Under Maintenance\n\n"
            "Oops! USDT withdrawals are currently under maintenance\n"
            "We'll restore it as soon as possible!\n\n"
            "💡 Tip: You can still withdraw instantly using 🏦Bank"
        )
    
    async def format_maintenance_status_message(self) -> str:
        """Format maintenance status message for admin panel"""
        try:
            status = await self.get_maintenance_status()
            
            message = "⚙️ Hello, Welcome To Bot Maintenance Setup!\n\n"
            message += "Click buttons below to toggle maintenance modes on and off.\n\n"
            message += "Current Status:\n"
            
            general_status = "ON" if status["general_maintenance"] else "OFF"
            bank_status = "ON" if status["bank_withdrawal_maintenance"] else "OFF"
            usdt_status = "ON" if status["usdt_withdrawal_maintenance"] else "OFF"
            
            message += f"▫️ General Maintenance: {general_status}\n"
            message += f"▫️ Bank Withdrawals: {bank_status}\n"
            message += f"▫️ USDT Withdrawals: {usdt_status}"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting maintenance status message: {e}")
            return "⚙️ Hello, Welcome To Bot Maintenance Setup!\n\nError loading maintenance status."
    
    async def format_withdrawal_maintenance_message(self) -> str:
        """Format withdrawal maintenance status message"""
        try:
            status = await self.get_maintenance_status()
            
            message = "💵 Withdrawal Maintenance Settings\n\n"
            message += "Current Status:\n"
            
            bank_status = "MAINTENANCE" if status["bank_withdrawal_maintenance"] else "ACTIVE"
            usdt_status = "MAINTENANCE" if status["usdt_withdrawal_maintenance"] else "ACTIVE"
            
            message += f"🏦 Bank Withdrawals: {bank_status}\n"
            message += f"💲 USDT Withdrawals: {usdt_status}\n\n"
            message += "Toggle individual withdrawal methods below:"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting withdrawal maintenance message: {e}")
            return "💵 Withdrawal Maintenance Settings\n\nError loading withdrawal status."
