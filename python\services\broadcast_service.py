"""
Broadcast service for sending messages to all users
Maintains identical functionality to PHP version
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp, generate_broadcast_id

logger = logging.getLogger(__name__)

class BroadcastService:
    """Service for broadcasting messages to users"""
    
    def __init__(self):
        self.bot = None
    
    def set_bot(self, bot: Bot):
        """Set the bot instance for broadcasting"""
        self.bot = bot
    
    async def broadcast_text_message(self, message: str, admin_id: int, exclude_banned: bool = True) -> Dict[str, Any]:
        """Broadcast text message to all users"""
        try:
            if not self.bot:
                return {
                    'success': False,
                    'message': 'Bot instance not set'
                }
            
            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Build query
            query = {}
            if exclude_banned:
                query['banned'] = {'$ne': True}
            
            cursor = users_collection.find(query, {'user_id': 1})
            users = await cursor.to_list(length=None)
            
            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            
            logger.info(f"Starting broadcast to {total_users} users")
            
            # Send messages in batches to avoid rate limiting
            batch_size = 30  # Telegram rate limit
            delay_between_batches = 1  # 1 second delay
            
            for i in range(0, len(users), batch_size):
                batch = users[i:i + batch_size]
                
                # Send to batch
                for user in batch:
                    try:
                        await self.bot.send_message(
                            chat_id=user['user_id'],
                            text=message,
                            parse_mode='HTML'
                        )
                        successful_sends += 1
                        
                        # Small delay between individual messages
                        await asyncio.sleep(0.05)
                        
                    except TelegramError as e:
                        failed_sends += 1
                        logger.warning(f"Failed to send broadcast to {user['user_id']}: {e}")
                    except Exception as e:
                        failed_sends += 1
                        logger.error(f"Error sending broadcast to {user['user_id']}: {e}")
                
                # Delay between batches
                if i + batch_size < len(users):
                    await asyncio.sleep(delay_between_batches)
            
            # Log broadcast completion
            await self._log_broadcast(admin_id, 'text', message, total_users, successful_sends, failed_sends)
            
            return {
                'success': True,
                'total_users': total_users,
                'successful_sends': successful_sends,
                'failed_sends': failed_sends,
                'message': f'Broadcast completed. Sent to {successful_sends}/{total_users} users.'
            }
            
        except Exception as e:
            logger.error(f"Error in broadcast_text_message: {e}")
            return {
                'success': False,
                'message': f'Broadcast failed: {str(e)}'
            }
    
    async def broadcast_gift_message(self, gift_amount: float, admin_id: int, exclude_banned: bool = True) -> Dict[str, Any]:
        """Broadcast gift message and add balance to all users"""
        try:
            if not self.bot:
                return {
                    'success': False,
                    'message': 'Bot instance not set'
                }
            
            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Build query
            query = {}
            if exclude_banned:
                query['banned'] = {'$ne': True}
            
            cursor = users_collection.find(query, {'user_id': 1, 'first_name': 1})
            users = await cursor.to_list(length=None)
            
            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            total_gift_amount = 0
            
            logger.info(f"Starting gift broadcast to {total_users} users")
            
            # Prepare gift message
            gift_message = f"🎁 <b>Gift Alert!</b>\n\n"
            gift_message += f"You have received ₹{gift_amount} as a gift from admin!\n\n"
            gift_message += f"💰 <b>Gift Amount:</b> ₹{gift_amount}\n"
            gift_message += f"🎉 <b>Enjoy your gift!</b>"
            
            # Send messages and add balance in batches
            batch_size = 30
            delay_between_batches = 1
            
            for i in range(0, len(users), batch_size):
                batch = users[i:i + batch_size]
                
                # Process batch
                for user in batch:
                    try:
                        # Add balance to user
                        await users_collection.update_one(
                            {'user_id': user['user_id']},
                            {'$inc': {'balance': gift_amount}}
                        )
                        
                        # Send gift message
                        await self.bot.send_message(
                            chat_id=user['user_id'],
                            text=gift_message,
                            parse_mode='HTML'
                        )
                        
                        successful_sends += 1
                        total_gift_amount += gift_amount
                        
                        # Small delay between individual messages
                        await asyncio.sleep(0.05)
                        
                    except TelegramError as e:
                        failed_sends += 1
                        logger.warning(f"Failed to send gift to {user['user_id']}: {e}")
                    except Exception as e:
                        failed_sends += 1
                        logger.error(f"Error sending gift to {user['user_id']}: {e}")
                
                # Delay between batches
                if i + batch_size < len(users):
                    await asyncio.sleep(delay_between_batches)
            
            # Log broadcast completion
            await self._log_broadcast(admin_id, 'gift', f"Gift: ₹{gift_amount}", total_users, successful_sends, failed_sends)
            
            return {
                'success': True,
                'total_users': total_users,
                'successful_sends': successful_sends,
                'failed_sends': failed_sends,
                'total_gift_amount': total_gift_amount,
                'message': f'Gift broadcast completed. Sent ₹{gift_amount} to {successful_sends}/{total_users} users. Total distributed: ₹{total_gift_amount}'
            }
            
        except Exception as e:
            logger.error(f"Error in broadcast_gift_message: {e}")
            return {
                'success': False,
                'message': f'Gift broadcast failed: {str(e)}'
            }
    
    async def _log_broadcast(self, admin_id: int, broadcast_type: str, content: str, total_users: int, successful: int, failed: int):
        """Log broadcast activity"""
        try:
            logs_collection = await get_collection(COLLECTIONS['admin_logs'])
            
            log_entry = {
                'admin_id': admin_id,
                'action': f'broadcast_{broadcast_type}',
                'details': {
                    'type': broadcast_type,
                    'content': content[:100],  # Truncate long content
                    'total_users': total_users,
                    'successful_sends': successful,
                    'failed_sends': failed
                },
                'timestamp': get_current_timestamp()
            }
            
            await logs_collection.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging broadcast: {e}")
    
    async def get_broadcast_statistics(self) -> Dict[str, Any]:
        """Get broadcast statistics"""
        try:
            logs_collection = await get_collection(COLLECTIONS['admin_logs'])
            
            # Get recent broadcasts
            cursor = logs_collection.find(
                {'action': {'$in': ['broadcast_text', 'broadcast_gift']}},
                sort=[('timestamp', -1)],
                limit=10
            )
            recent_broadcasts = await cursor.to_list(length=None)
            
            # Get total broadcast count
            total_broadcasts = await logs_collection.count_documents({
                'action': {'$in': ['broadcast_text', 'broadcast_gift']}
            })
            
            return {
                'success': True,
                'total_broadcasts': total_broadcasts,
                'recent_broadcasts': recent_broadcasts
            }
            
        except Exception as e:
            logger.error(f"Error getting broadcast statistics: {e}")
            return {
                'success': False,
                'message': f'Error getting statistics: {str(e)}'
            }

    # ==================== NEW ADMIN BROADCAST SYSTEM ====================

    async def get_broadcast_draft(self, admin_id: int) -> Optional[Dict[str, Any]]:
        """Get broadcast draft for admin"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])
            draft = await collection.find_one({"admin_id": admin_id, "status": "draft"})
            return draft
        except Exception as e:
            logger.error(f"Error getting broadcast draft: {e}")
            return None

    async def save_broadcast_draft(self, admin_id: int, data: Dict[str, Any]) -> bool:
        """Save or update broadcast draft"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            # Update existing draft or create new one
            result = await collection.update_one(
                {"admin_id": admin_id, "status": "draft"},
                {
                    "$set": {
                        **data,
                        "admin_id": admin_id,
                        "status": "draft",
                        "updated_at": get_current_timestamp()
                    }
                },
                upsert=True
            )

            return result.acknowledged

        except Exception as e:
            logger.error(f"Error saving broadcast draft: {e}")
            return False

    async def start_broadcast(self, admin_id: int, broadcast_data: Dict[str, Any]) -> Optional[str]:
        """Initialize broadcast and return broadcast ID"""
        try:
            broadcast_id = generate_broadcast_id()

            # Get total user count
            users_collection = await get_collection(COLLECTIONS['users'])
            total_users = await users_collection.count_documents({})

            # Create broadcast log entry
            broadcast_log = {
                "broadcast_id": broadcast_id,
                "admin_id": admin_id,
                "status": "in_progress",
                "broadcast_data": broadcast_data,
                "total_users": total_users,
                "successful_sends": 0,
                "failed_sends": 0,
                "blocked_users": 0,
                "started_at": get_current_timestamp(),
                "completed_at": None
            }

            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            result = await collection.insert_one(broadcast_log)

            if result.inserted_id:
                # Log broadcast initiation
                from services.admin_logging_service import AdminLoggingService
                logging_service = AdminLoggingService()
                await logging_service.log_broadcast_started(
                    admin_id,
                    broadcast_id,
                    "user_broadcast",
                    total_users
                )
                return broadcast_id

            return None

        except Exception as e:
            logger.error(f"Error starting broadcast: {e}")
            return None

    async def execute_broadcast(self, broadcast_id: str, query) -> None:
        """Execute broadcast with real-time progress updates"""
        try:
            from telegram import CallbackQuery

            # Get broadcast details
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            broadcast = await collection.find_one({"broadcast_id": broadcast_id})

            if not broadcast:
                await query.edit_message_text("❌ Broadcast not found.")
                return

            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            cursor = users_collection.find({})
            users = await cursor.to_list(length=None)

            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            blocked_users = 0

            broadcast_data = broadcast['broadcast_data']

            # Initialize bot if not set
            if not self.bot:
                from config.settings import settings
                from telegram import Bot
                self.bot = Bot(settings.BOT_TOKEN)

            # Process users in batches
            batch_size = 10
            for i in range(0, total_users, batch_size):
                batch = users[i:i + batch_size]

                # Process batch
                for user in batch:
                    try:
                        user_id = user['user_id']

                        # Prepare message for user
                        message_text = broadcast_data.get('text', '')
                        if message_text:
                            # Replace placeholders
                            message_text = message_text.replace('%firstname%', user.get('first_name', 'User'))
                            message_text = message_text.replace('%username%', user.get('username', 'N/A'))
                            message_text = message_text.replace('%mention%', f"@{user.get('username', 'user')}")

                        # Send message
                        if broadcast_data.get('media'):
                            # Send media message
                            media_data = broadcast_data['media']
                            if media_data['type'] == 'photo':
                                await self.bot.send_photo(
                                    chat_id=user_id,
                                    photo=media_data['file_id'],
                                    caption=message_text if message_text else None
                                )
                            elif media_data['type'] == 'video':
                                await self.bot.send_video(
                                    chat_id=user_id,
                                    video=media_data['file_id'],
                                    caption=message_text if message_text else None
                                )
                            # Add other media types as needed
                        else:
                            # Send text message
                            await self.bot.send_message(
                                chat_id=user_id,
                                text=message_text,
                                parse_mode='HTML'
                            )

                        successful_sends += 1

                    except Exception as e:
                        error_str = str(e).lower()
                        if "blocked" in error_str or "user is deactivated" in error_str or "chat not found" in error_str:
                            blocked_users += 1
                        else:
                            failed_sends += 1
                        logger.warning(f"Failed to send to user {user.get('user_id')}: {e}")

                # Update progress every batch
                users_left = total_users - (successful_sends + failed_sends + blocked_users)
                progress_message = f"⏳ Sleeping for 1 seconds\n\n"
                progress_message += f"✅ Broadcasted To: {successful_sends}\n"
                progress_message += f"🗨 Users Left: {users_left}"

                try:
                    await query.edit_message_text(progress_message)
                except:
                    pass  # Ignore edit errors

                # Small delay between batches
                await asyncio.sleep(1)

            # Calculate final statistics
            success_rate = round((successful_sends / total_users) * 100, 2) if total_users > 0 else 0

            # Update broadcast log
            await collection.update_one(
                {"broadcast_id": broadcast_id},
                {
                    "$set": {
                        "status": "completed",
                        "successful_sends": successful_sends,
                        "failed_sends": failed_sends,
                        "blocked_users": blocked_users,
                        "success_rate": success_rate,
                        "completed_at": get_current_timestamp()
                    }
                }
            )

            # Log broadcast completion
            from services.admin_logging_service import AdminLoggingService
            logging_service = AdminLoggingService()
            await logging_service.log_broadcast_completed(
                broadcast['admin_id'],
                broadcast_id,
                {
                    "total_users": total_users,
                    "successful_sends": successful_sends,
                    "failed_sends": failed_sends,
                    "blocked_users": blocked_users,
                    "success_rate": success_rate
                }
            )

            # Show final results
            final_message = "✅ Message Broadcast Completed!\n\n"
            final_message += f"🆔 Broadcast ID: {broadcast_id}\n"
            final_message += f"👥 Total Users: {total_users}\n"
            final_message += f"✅ Successfully Sent: {successful_sends}\n"
            final_message += f"❌ Failed: {failed_sends}\n"
            final_message += f"🚫 Blocked Users: {blocked_users}\n"
            final_message += f"📊 Success Rate: {success_rate}%\n"
            final_message += f"⏱️ Duration: Completed\n\n"
            final_message += "🎉 Broadcast completed successfully!"

            await query.edit_message_text(final_message)

        except Exception as e:
            logger.error(f"Error executing broadcast: {e}")
            await query.edit_message_text(
                f"❌ <b>Broadcast Error</b>\n\nSomething went wrong during broadcast execution."
            )
