"""
Withdrawal data models
Maintains identical structure to PHP JSON format
"""

from typing import Dict, List, Any, Optional
from utils.helpers import get_current_timestamp, get_current_date

class WithdrawalModel:
    """Withdrawal data model matching PHP structure exactly"""
    
    @staticmethod
    def create_withdrawal_record(
        user_id: int,
        amount: int,
        status: str = "Under review",
        method: str = "bank",
        date: str = None
    ) -> Dict[str, Any]:
        """Create withdrawal record (matching PHP structure)"""
        
        if date is None:
            date = get_current_date()
        
        return {
            "user_id": user_id,
            "amount": amount,
            "status": status,
            "method": method,
            "date": date,
            "created_at": get_current_timestamp(),
            "updated_at": get_current_timestamp()
        }
    
    @staticmethod
    def calculate_withdrawal_tax(amount: int, tax_settings: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate withdrawal tax (matching PHP logic exactly)"""
        tax_type = tax_settings.get('tax_type', 'none')
        tax_amount = tax_settings.get('tax_amount', 0)
        tax_percentage = tax_settings.get('tax_percentage', 0)
        
        original_amount = amount
        tax_deducted = 0
        final_amount = amount
        
        if tax_type == 'fixed' and tax_amount > 0:
            tax_deducted = tax_amount
            final_amount = max(0, amount - tax_amount)
        elif tax_type == 'percentage' and tax_percentage > 0:
            tax_deducted = int((amount * tax_percentage) / 100)
            final_amount = amount - tax_deducted
        
        return {
            'original_amount': original_amount,
            'tax_deducted': tax_deducted,
            'final_amount': final_amount,
            'tax_type': tax_type,
            'tax_rate': tax_amount if tax_type == 'fixed' else tax_percentage
        }
    
    @staticmethod
    def validate_withdrawal_amount(amount: int, user_balance: int, min_amount: int = 100) -> Dict[str, Any]:
        """Validate withdrawal amount"""
        errors = []
        
        if amount < min_amount:
            errors.append(f"Minimum withdrawal amount is ₹{min_amount}")
        
        if amount > user_balance:
            errors.append("Insufficient balance")
        
        if amount <= 0:
            errors.append("Invalid amount")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    @staticmethod
    def get_withdrawal_amounts() -> List[int]:
        """Get available withdrawal amounts (matching PHP config)"""
        return [100, 200, 400, 600, 800, 1000]
    
    @staticmethod
    def format_withdrawal_confirmation_message(
        user: Dict[str, Any],
        amount: int,
        tax_calculation: Dict[str, Any],
        date: str
    ) -> str:
        """Format withdrawal confirmation message (matching PHP exactly)"""
        
        message = "✅ <b>Withdrawal Request Submitted</b>\n\n"
        message += f"💵 <b>Requested Amount:</b> ₹{amount}\n"
        
        if tax_calculation['tax_deducted'] > 0:
            message += f"🏛️ <b>Tax Deducted:</b> ₹{tax_calculation['tax_deducted']}\n"
            message += f"💰 <b>Final Amount:</b> ₹{tax_calculation['final_amount']}\n"
        
        message += f"⏱️ <b>Date:</b> {date}\n"
        message += f"📋 <b>Status:</b> Under review\n\n"
        message += "The result will be notified in 1-3 working days."
        
        return message
    
    @staticmethod
    def format_admin_notification_message(
        user: Dict[str, Any],
        amount: int,
        withdrawal_method: str
    ) -> str:
        """Format admin notification message (matching PHP exactly)"""
        
        method_display = 'USDT (Binance ID)' if withdrawal_method == 'usdt' else 'Bank Account'
        
        message = f"<b>🆕 New withdrawal requested by {user['first_name']}\n\n"
        message += f"ℹ️ User ID :</b> <code>{user['user_id']}</code>\n"
        message += f"<b>💵 Requested Amount :</b> <code>₹{amount}</code>\n"
        message += f"<b>🔧 Withdrawal Method :</b> {method_display}\n"
        
        account_info = user.get('account_info', {})
        
        if withdrawal_method == 'usdt':
            binance_id = account_info.get('binance_id', account_info.get('usdt_address', ''))
            message += f"\n<b>👇 USDT Details :\n\n₿ Binance ID :</b> <code>{binance_id}</code>\n\n"
            message += "<b>✔️ Use the buttons below to approve or reject this withdrawal request.</b>"
        else:
            name = account_info.get('name', '')
            ifsc = account_info.get('ifsc', '')
            email = account_info.get('email', '')
            account_number = account_info.get('account_number', '')
            mobile_number = account_info.get('mobile_number', '')
            
            message += f"\n<b>👇 Bank Account Details :\n\n"
            message += f"Name : {name}\n"
            message += f"IFSC :</b> <code>{ifsc}</code>\n"
            message += f"<b>Email : {email}\n"
            message += f"Account Number :</b> <code>{account_number}</code>\n"
            message += f"<b>Mobile Number :</b> <code>{mobile_number}</code>\n\n"
            message += "<b>✔️ Use the buttons below to approve or reject this withdrawal request.</b>"
        
        return message
    
    @staticmethod
    def format_withdrawal_approval_message(
        user: Dict[str, Any],
        amount: int,
        date: str
    ) -> str:
        """Format withdrawal approval message for user (matching PHP exactly)"""
        
        message = "✅ <b>Withdrawal Approved!</b>\n\n"
        message += f"💵 <b>Amount:</b> ₹{amount}\n"
        message += f"⏰ <b>Date:</b> {date}\n\n"
        message += "🎉 Your withdrawal request has been approved!\n"
        message += "💳 The payment will be credited to your account in <b>1-2 working days</b>.\n\n"
        message += "📧 You will receive a confirmation email once the payment is processed.\n"
        message += "💬 For any queries, contact our support team."
        
        return message
    
    @staticmethod
    def format_withdrawal_rejection_message(
        user: Dict[str, Any],
        amount: int,
        date: str
    ) -> str:
        """Format withdrawal rejection message for user (matching PHP exactly)"""
        
        message = "❌ <b>Withdrawal Rejected</b>\n\n"
        message += f"💵 <b>Amount:</b> ₹{amount}\n"
        message += f"⏰ <b>Date:</b> {date}\n\n"
        message += "😔 Your withdrawal request has been rejected.\n"
        message += "💰 The amount has been refunded to your balance.\n\n"
        message += "📞 Please contact our support team for more information about the rejection reason."
        
        return message
    
    @staticmethod
    def format_updated_admin_message(
        user: Dict[str, Any],
        amount: int,
        status: str,
        admin_name: str,
        processed_date: str
    ) -> str:
        """Format updated admin message after processing (matching PHP exactly)"""
        
        status_emoji = '✅' if status == 'APPROVED' else '❌'
        
        message = f"<b>{status_emoji} Withdrawal {status}</b>\n\n"
        message += f"<b>👤 User:</b> {user['first_name']}\n"
        message += f"<b>ℹ️ User ID:</b> <code>{user['user_id']}</code>\n"
        message += f"<b>💵 Amount:</b> <code>₹{amount}</code>\n\n"
        message += f"<b>👨‍💼 Processed by:</b> {admin_name}\n"
        message += f"<b>📅 Processed on:</b> {processed_date}\n"
        message += f"<b>📊 Status:</b> {status}"
        
        return message

class AccountInfoModel:
    """Account information model for withdrawal methods"""
    
    @staticmethod
    def get_default_account_info() -> Dict[str, Any]:
        """Get default account info structure"""
        return {
            'name': '',
            'ifsc': '',
            'email': '',
            'account_number': '',
            'mobile_number': '',
            'withdrawal_method': 'bank',
            'usdt_address': '',
            'binance_id': ''
        }
    
    @staticmethod
    def validate_bank_account_info(account_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate bank account information"""
        required_fields = ['name', 'ifsc', 'email', 'account_number', 'mobile_number']
        missing_fields = []
        
        for field in required_fields:
            if not account_info.get(field, '').strip():
                missing_fields.append(field)
        
        return {
            'valid': len(missing_fields) == 0,
            'missing_fields': missing_fields
        }
    
    @staticmethod
    def validate_usdt_account_info(account_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate USDT account information"""
        binance_id = account_info.get('binance_id', account_info.get('usdt_address', '')).strip()
        
        if not binance_id:
            return {
                'valid': False,
                'missing_fields': ['binance_id']
            }
        
        return {
            'valid': True,
            'missing_fields': []
        }
    
    @staticmethod
    def format_account_info_message(user: Dict[str, Any]) -> str:
        """Format account info display message (matching PHP exactly)"""
        account_info = user.get('account_info', {})
        withdrawal_method = account_info.get('withdrawal_method', 'bank')
        
        if withdrawal_method == 'usdt':
            message = "₿ <b>USDT (Binance ID) Setup</b>\n\n"
            message += "⚙️ Please set your Binance ID for USDT withdrawals.\n"
            message += "⚠️ Make sure to provide a valid Binance ID!\n\n"
            message += "✅ <b>Current Method:</b> USDT (Binance ID)\n\n"
            
            binance_id = account_info.get('binance_id', account_info.get('usdt_address', ''))
            message += f"<b>Binance ID:</b> {binance_id if binance_id else '<i>Not set</i>'}\n\n"
            message += "💡 <b>Note:</b> Your Binance ID is your unique identifier on Binance."
        else:
            message = "🏦 <b>Bank Account Setup</b>\n\n"
            message += "⚙️ Please set your bank account details for withdrawals.\n"
            message += "⚠️ Make sure all information is accurate!\n\n"
            message += "✅ <b>Current Method:</b> Bank Account\n\n"
            
            name = account_info.get('name', '')
            ifsc = account_info.get('ifsc', '')
            email = account_info.get('email', '')
            account_number = account_info.get('account_number', '')
            mobile_number = account_info.get('mobile_number', '')
            
            message += f"<b>Name:</b> {name if name else '<i>Not set</i>'}\n"
            message += f"<b>IFSC:</b> {ifsc if ifsc else '<i>Not set</i>'}\n"
            message += f"<b>Email:</b> {email if email else '<i>Not set</i>'}\n"
            message += f"<b>Account Number:</b> {account_number if account_number else '<i>Not set</i>'}\n"
            message += f"<b>Mobile Number:</b> {mobile_number if mobile_number else '<i>Not set</i>'}\n\n"
            message += "💡 <b>Note:</b> All fields are required for bank transfers."
        
        return message
