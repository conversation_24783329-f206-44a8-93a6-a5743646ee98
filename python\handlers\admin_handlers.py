"""
Admin command handlers - Original Structure with Enhanced Functionality
Maintains identical functionality to PHP version with improved performance
"""

import logging
from typing import Optional, Dict, Any, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TimedOut, NetworkError

from services.admin_service import AdminService
from services.user_service import UserService
from utils.helpers import is_admin, get_rank_emoji, get_current_date
from utils.constants import ADMIN_ACCESS_DENIED

logger = logging.getLogger(__name__)

class AdminHandlers:
    """Handles admin-related commands and interactions - Original Structure"""
    
    def __init__(self):
        self.admin_service = AdminService()
        self.user_service = UserService()
    
    async def handle_admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /admin command and 'admin' callback - NEW Enhanced Admin Panel"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id

        try:
            if not is_admin(user_id):
                return

            # Get new enhanced admin dashboard
            admin_menu = await self._get_new_admin_dashboard()
            admin_message = await self._get_new_admin_message()

            # Check if this is a callback query (navigation) or text command
            if update.callback_query:
                # This is navigation from a "Back to Admin Panel" button
                await update.callback_query.edit_message_text(
                    admin_message,
                    reply_markup=admin_menu,
                    parse_mode='HTML'
                )
            else:
                # This is the /admin text command
                await update.message.reply_text(
                    admin_message,
                    reply_markup=admin_menu,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_admin_command: {e}")

            # Handle error response based on update type
            try:
                if update.callback_query:
                    await update.callback_query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
                elif update.message:
                    await update.message.reply_text(
                        "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                        parse_mode='HTML'
                    )
                else:
                    logger.error("Both callback_query and message are None in handle_admin_command")
            except Exception as error_e:
                logger.error(f"Error in error handling for handle_admin_command: {error_e}")

    # ==================== OLD ADMIN PANEL METHODS (COMMENTED OUT) ====================
    # These methods are preserved for reference but not used in the new system

    # async def _get_original_admin_message(self) -> str:
    #     """Get original admin dashboard message matching PHP version"""
    #     try:
    #         from services.admin_service import AdminService
    #         admin_service = AdminService()
    #         admin_settings = await admin_service.get_admin_settings()

    #         bot_username = admin_settings.get('bot_username', 'Unknown')
    #         main_channel = admin_settings.get('main_channel', 'Not set')
    #         private_logs_channel = admin_settings.get('private_logs_channel', 'Not set')
    #         maintenance_status = admin_settings.get('maintenance_status', 'Off')
    #         otp_api_key = admin_settings.get('otp_website_api_key', 'Not set')
    #         per_refer_amount = admin_settings.get('per_refer_amount', '0')
    #         joining_bonus_amount = admin_settings.get('joining_bonus_amount', '0')

    #         # Get referral and bonus ranges
    #         per_refer_range = admin_settings.get('per_refer_amount_range', '1-3')
    #         joining_bonus_range = admin_settings.get('joining_bonus_amount_range', '49-55')

    #         # Format ranges for display
    #         referral_range_display = f"₹{per_refer_range}"
    #         bonus_range_display = f"₹{joining_bonus_range}"

    #         # Create the original admin message format
    #         message = f"<b>👋 Hello bro, welcome to the admin panel of @{bot_username}.\n\n"
    #         message += f"🏘️ Main channel : @{main_channel}\n"
    #         message += f"🤫 Private logs channel : {private_logs_channel}\n"
    #         message += f"⚙️ Maintenance status :</b> <code>{maintenance_status}</code>\n"
    #         message += f"<b>🔐 OTP website API key :</b> <code>{otp_api_key}</code>\n"
    #         message += f"<b>🧑‍🤝‍🧑 Per refer amount :</b> <code>{referral_range_display}</code>\n"
    #         message += f"<b>💸 Joining bonus amount :</b> <code>{bonus_range_display}</code>\n\n"
    #         message += f"<b>⚠️ Note :</b> <i>Send /admin again after setting all the things to see the changes.</i>"

    #         return message

    #     except Exception as e:
    #         logger.error(f"Error getting original admin message: {e}")
    #         return "<b>👋 Hello bro, welcome to the admin panel.\n\n⚠️ Note :</b> <i>Send /admin again after setting all the things to see the changes.</i>"

    # async def _get_original_admin_dashboard(self) -> InlineKeyboardMarkup:
    #     """Get original admin dashboard matching PHP version exactly"""
    #     keyboard = [
    #         [
    #             InlineKeyboardButton('➕ Add balance', callback_data='add'),
    #             InlineKeyboardButton('➖ Remove balance', callback_data='remove')
    #         ],
    #         [
    #             InlineKeyboardButton('🚫 Ban user', callback_data='ban'),
    #             InlineKeyboardButton('✔️ Unban user', callback_data='unban')
    #         ],
    #         [
    #             InlineKeyboardButton('🏘️ Set / Change main channel', callback_data='mainChannel')
    #         ],
    #         [
    #             InlineKeyboardButton('🤫 Set / Change private logs channel', callback_data='privateLogsChannel')
    #         ],
    #         [
    #             InlineKeyboardButton('➕ Add Force Sub Channel', callback_data='addForceSubChannel'),
    #             InlineKeyboardButton('➖ Remove Force Sub Channel', callback_data='removeForceSubChannel')
    #         ],
    #         [
    #             InlineKeyboardButton('📋 View Force Sub Channels', callback_data='viewForceSubChannels')
    #         ],
    #         [
    #             InlineKeyboardButton('⚙️ Set / Change maintenance status', callback_data='maintenanceStatus')
    #         ],
    #         [
    #             InlineKeyboardButton('🔐 Set / Change OTP website API key', callback_data='OTPWebsiteAPIKey')
    #         ],
    #         [
    #             InlineKeyboardButton('🧑‍🤝‍🧑 Set / Change per refer amount', callback_data='perReferAmount')
    #         ],
    #         [
    #             InlineKeyboardButton('💸 Set / Change joining bonus amount', callback_data='joiningBonusAmount')
    #         ],
    #         [
    #             InlineKeyboardButton('🏛️ Withdrawal Tax & Control', callback_data='withdrawal_settings')
    #         ],
    #         [
    #             InlineKeyboardButton('🔍 Check user\'s record', callback_data='checkUserRecord')
    #         ],
    #         [
    #             InlineKeyboardButton('🏧 Pass user\'s withdrawal', callback_data='passUserWithdrawal')
    #         ],
    #         [
    #             InlineKeyboardButton('🏧 Fail user\'s withdrawal', callback_data='failUserWithdrawal')
    #         ],
    #         [
    #             InlineKeyboardButton('🔄 Reset User Account Details', callback_data='resetUserAccount')
    #         ],
    #         [
    #             InlineKeyboardButton('🎁 Broadcast gift button', callback_data='broadcastGiftButton')
    #         ],
    #         [
    #             InlineKeyboardButton('📢 Broadcast Message', callback_data='broadcastText')
    #         ],
    #         [
    #             InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks'),
    #             InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')
    #         ],
    #         [
    #             InlineKeyboardButton('🎫 Generate Gift Code', callback_data='generateGiftCode')
    #         ],
    #         [
    #             InlineKeyboardButton('🏆 Configure Level Rewards', callback_data='configureLevelRewards'),
    #             InlineKeyboardButton('🔄 Toggle Level Bonus', callback_data='toggleLevelBonus')
    #         ],
    #         [
    #             InlineKeyboardButton('🔗 Custom Referral Links', callback_data='customReferralLinks')
    #         ]
    #     ]

    #     return InlineKeyboardMarkup(keyboard)

    # ==================== NEW ENHANCED ADMIN PANEL SYSTEM ====================

    async def _get_new_admin_message(self) -> str:
        """Get new enhanced admin panel message with exact specifications"""
        try:
            import time
            from telegram import Bot
            from config.settings import settings

            # Get bot statistics
            stats = await self.admin_service.get_bot_statistics()
            admin_settings = await self.admin_service.get_admin_settings()

            # Calculate bot ping
            start_time = time.time()
            try:
                bot = Bot(token=settings.BOT_TOKEN)
                await bot.get_me()
                ping_time = round((time.time() - start_time) * 1000, 2)
                bot_status = "🟢 Live"
            except Exception:
                ping_time = "N/A"
                bot_status = "🔴 Offline"

            # Get withdrawal settings
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})
            withdrawal_enabled = withdrawal_settings.get('enabled', True)
            withdrawal_status = "🟢 Enabled" if withdrawal_enabled else "🔴 Disabled"

            # Get maintenance status
            maintenance_status = admin_settings.get('maintenance_status', 'Off')
            maintenance_display = "Enabled" if maintenance_status == 'On' else "Disabled"

            # Get per refer and join bonus ranges
            per_refer_range = admin_settings.get('per_refer_amount_range', '20-50')
            join_bonus_range = admin_settings.get('joining_bonus_amount_range', '20-50')

            # Get total users and withdrawn amount
            total_users = stats.get('total_users', 0)
            total_withdrawn = stats.get('total_successful_withdrawals', 0)

            # Create the exact message format
            message = f"⚙️ Hello Admin, Welcome To Bot Settings.\n\n"
            message += f"▫️ Bot Status: {bot_status} (Ping: {ping_time}ms)\n"
            message += f"▫️ Withdrawal Status: {withdrawal_status}\n"
            message += f"▫️ Maintenance Status: {maintenance_display} (toggleable)\n"
            message += f"▫️ Per Refer: ₹{per_refer_range} (from settings)\n"
            message += f"▫️ Join Bonus: ₹{join_bonus_range} (from settings)\n"
            message += f"▫️ Total Users: {total_users}\n"
            message += f"▫️ Withdrawn: ₹{total_withdrawn}"

            return message

        except Exception as e:
            logger.error(f"Error getting new admin message: {e}")
            return "⚙️ Hello Admin, Welcome To Bot Settings.\n\n❌ Error loading bot statistics."

    async def _get_new_admin_dashboard(self) -> InlineKeyboardMarkup:
        """Get new enhanced admin dashboard with exact button hierarchy"""
        try:
            keyboard = [
                [
                    InlineKeyboardButton('💰 Manage Withdrawals', callback_data='manage_withdrawals')
                ],
                [
                    InlineKeyboardButton('💰 User Bonus', callback_data='user_bonus')
                ],
                [
                    InlineKeyboardButton('👤 User Details & Settings', callback_data='user_details_settings')
                ],
                [
                    InlineKeyboardButton('📋 Manage Tasks', callback_data='manage_tasks')
                ],
                [
                    InlineKeyboardButton('👨‍💼 Manage Admins', callback_data='manage_admins')
                ],
                [
                    InlineKeyboardButton('📬 User Broadcast', callback_data='user_broadcast')
                ],
                [
                    InlineKeyboardButton('📊 Bot Statistics & Analysis', callback_data='bot_statistics')
                ],
                [
                    InlineKeyboardButton('🎁 Manage Redeem Codes', callback_data='manage_gift_codes')
                ],
                [
                    InlineKeyboardButton('🌟 ForceSub Channels', callback_data='forcesub_channels')
                ],
                [
                    InlineKeyboardButton('🎁 Gift Broadcast', callback_data='gift_broadcast')
                ],
                [
                    InlineKeyboardButton('⚠️ Bot Maintenance', callback_data='bot_maintenance')
                ],
                [
                    InlineKeyboardButton('🔗 Custom Referral Link', callback_data='custom_referral_link')
                ]
            ]

            return InlineKeyboardMarkup(keyboard)

        except Exception as e:
            logger.error(f"Error getting new admin dashboard: {e}")
            # Fallback keyboard
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('💰 Manage Withdrawals', callback_data='manage_withdrawals')],
                [InlineKeyboardButton('💰 User Bonus', callback_data='user_bonus')],
                [InlineKeyboardButton('👤 User Details & Settings', callback_data='user_details_settings')],
                [InlineKeyboardButton('📋 Manage Tasks', callback_data='manage_tasks')],
                [InlineKeyboardButton('👨‍💼 Manage Admins', callback_data='manage_admins')],
                [InlineKeyboardButton('📬 User Broadcast', callback_data='user_broadcast')],
                [InlineKeyboardButton('📊 Bot Statistics & Analysis', callback_data='bot_statistics')],
                [InlineKeyboardButton('🎁 Manage Redeem Codes', callback_data='manage_gift_codes')],
                [InlineKeyboardButton('🌟 ForceSub Channels', callback_data='forcesub_channels')],
                [InlineKeyboardButton('🎁 Gift Broadcast', callback_data='gift_broadcast')],
                [InlineKeyboardButton('⚠️ Bot Maintenance', callback_data='bot_maintenance')],
                [InlineKeyboardButton('🔗 Custom Referral Link', callback_data='custom_referral_link')]
            ])

    # ==================== NEW ADMIN PANEL HANDLERS ====================

    # ==================== TASK MANAGEMENT PANEL ====================

    async def handle_manage_tasks(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Task Management Panel"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get task statistics
            from services.task_service import TaskService
            task_service = TaskService()

            # Get all tasks and statistics
            all_tasks = await task_service.get_all_tasks()
            active_tasks = [task for task in all_tasks if task.get('status') == 'active']
            task_stats = await task_service.get_task_statistics()

            # Build message
            message = "⚙️ Hello, Welcome To The Task Settings.\n\n"
            message += f"🏷 Total Live Tasks: {len(active_tasks)}\n\n"
            message += f"▫️ Screenshots Submitted: {task_stats.get('total_submissions', 0)}\n"
            message += f"▫️ Total Screenshots Rejected: {task_stats.get('rejected_submissions', 0)}\n"
            message += f"▫️ Total Screenshots Approved: {task_stats.get('approved_submissions', 0)}\n"
            message += f"▫️ Total Pending Screenshots: {task_stats.get('pending_submissions', 0)}"

            # Build keyboard with dynamic task buttons
            keyboard_buttons = []

            # Add dynamic task buttons (only for existing tasks)
            if all_tasks:
                for task in all_tasks:
                    task_name = task['name']
                    # Truncate long task names for button display
                    display_name = task_name if len(task_name) <= 30 else task_name[:27] + "..."
                    keyboard_buttons.append([
                        InlineKeyboardButton(display_name, callback_data=f"manage_task_{task['task_id']}")
                    ])

            # Add static action buttons
            keyboard_buttons.extend([
                [InlineKeyboardButton('➕ Add Task', callback_data='add_task')],
                [InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')]
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            await query.edit_message_text(
                text=message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_tasks: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading task management. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_manage_individual_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle Individual Task Management Panel"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get task details
            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nTask not found.",
                    parse_mode='HTML'
                )
                return

            # Get task submission statistics
            pending_count = await task_service.get_pending_submissions_count(task_id)
            approved_count = await task_service.get_approved_submissions_count(task_id)
            rejected_count = await task_service.get_rejected_submissions_count(task_id)
            total_submissions = pending_count + approved_count + rejected_count

            # Build message
            message = "⚙️ Hello, Manage Task From Here.\n\n"
            message += f"▫️ Name: {task['name']}\n"
            message += f"▫️ Bonus Amount: ₹{task['reward_amount']}\n"
            message += f"▫️ Task Media URL: {task.get('media_url', 'Not set')}\n"
            message += f"▫️ Pending Screenshots: {pending_count}\n"
            message += f"📥 Screenshots Uploaded: {total_submissions} (Approved: {approved_count} | Rejected: {rejected_count})"

            # Build keyboard with task edit buttons
            keyboard = [
                [
                    InlineKeyboardButton('✏️ Edit Task Name', callback_data=f'edit_task_name_{task_id}')
                ],
                [
                    InlineKeyboardButton('💰 Edit Task Bonus', callback_data=f'edit_task_bonus_{task_id}')
                ],
                [
                    InlineKeyboardButton('📝 Edit Task Content', callback_data=f'edit_task_content_{task_id}')
                ],
                [
                    InlineKeyboardButton('🖼️ Edit Task Image URL', callback_data=f'edit_task_image_{task_id}')
                ],
                [
                    InlineKeyboardButton('⚠️ Delete Task', callback_data=f'delete_task_{task_id}')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_tasks')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_individual_task: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading task details. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_edit_task_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task name"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "✏️ <b>Edit Task Name</b>\n\n"
            message += "Send the new task name to set:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_name', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_name: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task bonus"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "💰 <b>Edit Task Bonus</b>\n\n"
            message += "Send the new task bonus amount to set:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_bonus', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_content(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task content"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📝 <b>Edit Task Content</b>\n\n"
            message += "Send the new task content/steps to set:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_content', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_content: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_image(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task image URL"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🖼️ <b>Edit Task Image URL</b>\n\n"
            message += "Send the new image URL to set:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_image', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_image: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle delete task"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Delete the task
            from services.task_service import TaskService
            task_service = TaskService()

            success = await task_service.delete_task(task_id)

            if success:
                await query.answer("✅ Task Has Been Successfully Deleted..✅️", show_alert=True)

                # Show go back button
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Go Back', callback_data='manage_tasks')]
                ])

                await query.edit_message_text(
                    "✅ <b>Task Deleted Successfully</b>\n\nThe task has been removed from the system.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await query.answer("❌ Failed to delete task.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_delete_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_add_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle add new task workflow"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add New Task</b>\n\n"
            message += "Enter the Name and Bonus, separated by (-)\n\n"
            message += "e.g., Promote & Earn-5\n\n"
            message += "- Task Name: Promote & Earn\n"
            message += "- Bonus: ₹5\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_task_step1')

        except Exception as e:
            logger.error(f"Error in handle_add_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== ADMIN MANAGEMENT SYSTEM ====================

    async def handle_manage_admins(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Admin Management Panel (6.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current admin list
            admin_settings = await self.admin_service.get_admin_settings()
            admin_list = admin_settings.get('admin_list', [])

            # Build message
            message = "⚙️ Hello, Welcome To Admin Settings.\n\n"
            message += f"👨🏻‍💻 Total Admins Added: {len(admin_list)}\n\n"

            # Display numbered list of admin IDs
            if admin_list:
                for i, admin_id in enumerate(admin_list, 1):
                    message += f"{i}. {admin_id}\n"
            else:
                message += "No additional admins configured."

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton('➕ Add Admin', callback_data='add_admin')
                ],
                [
                    InlineKeyboardButton('➖ Remove Admin', callback_data='remove_admin')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_admins: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading admin management. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_add_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Add Admin workflow (6.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add Admin</b>\n\n"
            message += "Send user's chat ID to promote as admin:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_admin')

        except Exception as e:
            logger.error(f"Error in handle_add_admin: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Remove Admin workflow (6.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➖ <b>Remove Admin</b>\n\n"
            message += "Send user's chat ID to remove admin rights:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_admin')

        except Exception as e:
            logger.error(f"Error in handle_remove_admin: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== USER BROADCAST SYSTEM ====================

    async def handle_user_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle User Broadcast Panel (7.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📬 Broadcast\nSend a message to all bot users simultaneously."

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton('🖼️ Media', callback_data='broadcast_media')
                ],
                [
                    InlineKeyboardButton('🔤 Text', callback_data='broadcast_text')
                ],
                [
                    InlineKeyboardButton('⌨️ Buttons', callback_data='broadcast_buttons')
                ],
                [
                    InlineKeyboardButton('👀 Full Preview', callback_data='broadcast_preview')
                ],
                [
                    InlineKeyboardButton('▶️ Start Broadcast', callback_data='start_broadcast')
                ],
                [
                    InlineKeyboardButton('🔙 Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading broadcast panel. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_broadcast_media(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Broadcast Media Setup (7.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🖼️ <b>Media Setup</b>\n\n"
            message += "Send the new post media\n"
            message += "Allowed media: photos, videos, files, stickers, GIFs, audio, voice messages, round videos\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_media')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_media: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Broadcast Text Setup (7.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🔤 <b>Text Setup</b>\n\n"
            message += "Send the post message\n"
            message += "The following keywords can be added in the text and will be replaced with user data:\n\n"
            message += "• User first name: %firstname%\n"
            message += "• Username: %username%\n"
            message += "• User Mention: %mention%\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_text')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_text: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Broadcast Buttons Setup (7.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "⌨️ <b>Buttons Setup</b>\n\n"
            message += "Set the buttons to insert in the keyboard under the post\n"
            message += "Send a message structured as follows:\n\n"
            message += "• Insert multiple rows of buttons:\n"
            message += "Button text - t.me/LinkExample\n"
            message += "Button text - t.me/LinkExample\n\n"
            message += "• Insert multiple buttons in a single line:\n"
            message += "Button text - t.me/LinkExample && Button text - t.me/LinkExample\n\n"
            message += "• Insert a button that displays a popup:\n"
            message += "Button text - popup: Text of the popup\n"
            message += "or\n"
            message += "Button text - alert: Text of the popup\n\n"
            message += "• Insert a share button:\n"
            message += "Button text - share: Text to share\n\n"
            message += "• Insert a menu/form button:\n"
            message += "Button text - menu: menu name\n"
            message += "Button text - form: form name\n"
            message += "To return the user to the start menu put menu: start\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_buttons')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_buttons: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_preview(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Broadcast Preview (7.4)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get broadcast data from session or storage
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            broadcast_data = await broadcast_service.get_broadcast_draft(user_id)

            if not broadcast_data:
                await query.edit_message_text(
                    "❌ <b>No Broadcast Data</b>\n\nPlease configure media, text, or buttons first.",
                    parse_mode='HTML'
                )
                return

            # Build preview message
            preview_message = "👀 <b>Full Preview</b>\n\n"
            preview_message += "This is how your broadcast will appear to users:\n\n"
            preview_message += "─────────────────────\n"

            if broadcast_data.get('text'):
                # Replace placeholders with example data
                preview_text = broadcast_data['text']
                preview_text = preview_text.replace('%firstname%', 'John')
                preview_text = preview_text.replace('%username%', '@johndoe')
                preview_text = preview_text.replace('%mention%', '@johndoe')
                preview_message += f"{preview_text}\n"

            if broadcast_data.get('media'):
                preview_message += f"📎 Media: {broadcast_data['media']['type']}\n"

            if broadcast_data.get('buttons'):
                preview_message += f"⌨️ Buttons: {len(broadcast_data['buttons'])} configured\n"

            preview_message += "─────────────────────"

            keyboard = [
                [
                    InlineKeyboardButton('🔙 Back', callback_data='user_broadcast')
                ]
            ]

            await query.edit_message_text(
                text=preview_message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_broadcast_preview: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while generating preview. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_start_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Start Broadcast (7.5)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get broadcast data
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            broadcast_data = await broadcast_service.get_broadcast_draft(user_id)

            if not broadcast_data or not (broadcast_data.get('text') or broadcast_data.get('media')):
                await query.edit_message_text(
                    "❌ <b>No Broadcast Content</b>\n\nPlease configure text or media before starting broadcast.",
                    parse_mode='HTML'
                )
                return

            # Show confirmation with preview
            message = "📬 Broadcast\n\n"
            message += "Are you really sure you want to do this?\n\n"
            message += "<b>Preview:</b>\n"

            if broadcast_data.get('text'):
                preview_text = broadcast_data['text'][:100] + "..." if len(broadcast_data['text']) > 100 else broadcast_data['text']
                message += f"Text: {preview_text}\n"

            if broadcast_data.get('media'):
                message += f"Media: {broadcast_data['media']['type']}\n"

            if broadcast_data.get('buttons'):
                message += f"Buttons: {len(broadcast_data['buttons'])} configured\n"

            keyboard = [
                [
                    InlineKeyboardButton('❌ NO', callback_data='user_broadcast'),
                    InlineKeyboardButton('✅ YES', callback_data='confirm_broadcast')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_start_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while preparing broadcast. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_confirm_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Broadcast Execution Confirmation"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Start broadcast execution
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Get broadcast data
            broadcast_data = await broadcast_service.get_broadcast_draft(user_id)
            if not broadcast_data:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nBroadcast data not found.",
                    parse_mode='HTML'
                )
                return

            # Initialize broadcast
            broadcast_id = await broadcast_service.start_broadcast(user_id, broadcast_data)

            if not broadcast_id:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to start broadcast.",
                    parse_mode='HTML'
                )
                return

            # Show initial progress message
            progress_message = "⏳ Sleeping for 1 seconds\n\n"
            progress_message += "✅ Broadcasted To: 0\n"
            progress_message += "🗨 Users Left: Calculating..."

            await query.edit_message_text(
                text=progress_message,
                parse_mode='HTML'
            )

            # Execute broadcast in background
            await broadcast_service.execute_broadcast(broadcast_id, query)

        except Exception as e:
            logger.error(f"Error in handle_confirm_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while starting broadcast. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== BOT STATISTICS & ANALYSIS SYSTEM ====================

    async def handle_bot_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Bot Statistics & Analysis Panel (8.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📊 View Bot's Live Stats & Analysis.\n\n"
            message += "▫️ Get overall stats for the bot's performance.\n"
            message += "▫️ Get top 30 users based on balance/invites.\n"
            message += "▫️ Get separate join stats for each channel."

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton('📊 Overall Bot Statistics', callback_data='overall_statistics')
                ],
                [
                    InlineKeyboardButton('💰 Top 30 Balances', callback_data='top_balances')
                ],
                [
                    InlineKeyboardButton('👥 Top 30 Inviters', callback_data='top_inviters')
                ],
                [
                    InlineKeyboardButton('🏆 Top 30 Withdrawal Rank', callback_data='top_withdrawers')
                ],
                [
                    InlineKeyboardButton('📈 Channel Wise Stats', callback_data='channel_stats')
                ],
                [
                    InlineKeyboardButton('⏳ Pending Withdrawals', callback_data='pending_withdrawals')
                ],
                [
                    InlineKeyboardButton('👥 History Of Users', callback_data='user_history')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_bot_statistics: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading statistics panel. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_overall_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Overall Bot Statistics (8.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get statistics
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            stats = await stats_service.get_overall_statistics()

            if not stats:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to load statistics. Please try again later.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "📊 Overall Bot Statistics :-\n\n"
            message += f"👨🏻‍💼 Total Users: {stats.get('total_users', 0)} (Active Users: {stats.get('active_users', 0)})\n\n"

            message += "💰 Financial Statistics:\n"
            message += f"▫️ Total User Balance: ₹{stats.get('total_user_balance', 0):.2f}\n"
            message += f"▫️ Total Banks Used: {stats.get('unique_banks_count', 0)}\n"
            message += f"▫️ Total USDT Used: {stats.get('unique_usdt_count', 0)}\n\n"

            message += "💸 Withdrawal Statistics:\n"
            message += f"▫️ Total Withdrawals Count: {stats.get('completed_withdrawals', 0)}\n"
            message += f"▫️ Withdrawal With Tax: ₹{stats.get('total_withdrawn_with_tax', 0):.2f} || Without Tax: ₹{stats.get('total_withdrawn_without_tax', 0):.2f}\n"
            message += f"▫️ Pending Withdrawals: {stats.get('pending_withdrawals_count', 0)} requests (₹{stats.get('pending_withdrawals_amount', 0):.2f})\n\n"

            message += "🎁 Rewards & Activity:\n"
            message += f"▫️ Total Codes Claimed: {stats.get('claimed_codes_count', 0)}\n"
            message += f"▫️ Balance Added via Codes: ₹{stats.get('total_gift_value', 0):.2f}\n"
            message += f"▫️ Total Screenshots Submitted: {stats.get('total_task_submissions', 0)}"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='overall_statistics')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_overall_statistics: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading overall statistics. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_top_balances(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Top 30 Balances (8.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get top balances
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            top_users = await stats_service.get_top_balances(30)

            if not top_users:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo users with balance found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "💰 Top 30 Balances:\n\n"

            for i, user in enumerate(top_users, 1):
                username = user.get('username', 'N/A')
                first_name = user.get('first_name', 'Unknown')
                balance = user.get('balance', 0)
                user_id_str = str(user.get('user_id', 'N/A'))

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Balance: ₹{balance:.2f}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='top_balances')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_top_balances: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading top balances. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_top_inviters(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Top 30 Inviters (8.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get top inviters
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            top_users = await stats_service.get_top_inviters(30)

            if not top_users:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo users with referrals found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "👥 Top 30 Inviters:\n\n"

            for i, user in enumerate(top_users, 1):
                username = user.get('username', 'N/A')
                first_name = user.get('first_name', 'Unknown')
                total_referrals = user.get('total_referrals', 0)
                referral_earnings = user.get('referral_earnings', 0)
                user_id_str = str(user.get('user_id', 'N/A'))

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Invites: {total_referrals} | Earnings: ₹{referral_earnings:.2f}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='top_inviters')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_top_inviters: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading top inviters. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_top_withdrawers(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Top 30 Withdrawal Rank (8.4)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get top withdrawers
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            top_users = await stats_service.get_top_withdrawers(30)

            if not top_users:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo users with withdrawals found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "🏆 Top 30 Withdrawal Rank:\n\n"

            for i, user in enumerate(top_users, 1):
                username = user.get('username', 'N/A')
                first_name = user.get('first_name', 'Unknown')
                total_withdrawn = user.get('total_withdrawn', 0)
                withdrawal_count = user.get('withdrawal_count', 0)
                user_id_str = str(user.get('user_id', 'N/A'))

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Total: ₹{total_withdrawn:.2f} ({withdrawal_count} withdrawals)\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='top_withdrawers')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_top_withdrawers: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading top withdrawers. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_channel_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Channel Wise Stats (8.5)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get channel statistics
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            channel_stats = await stats_service.get_channel_statistics()

            if not channel_stats:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo channel statistics available.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "📈 Channel Wise Stats:\n\n"

            for i, channel in enumerate(channel_stats, 1):
                channel_name = channel.get('channel_name', 'Unknown')
                total_members = channel.get('total_members', 0)
                joined_today = channel.get('joined_today', 0)
                joined_yesterday = channel.get('joined_yesterday', 0)
                joined_this_week = channel.get('joined_this_week', 0)
                joined_this_month = channel.get('joined_this_month', 0)

                message += f"{i}. {channel_name}\n"
                message += f"   Total: {total_members} | Today: {joined_today}\n"
                message += f"   Yesterday: {joined_yesterday} | Week: {joined_this_week}\n"
                message += f"   Month: {joined_this_month}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='channel_stats')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_channel_stats: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading channel statistics. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_pending_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Pending Withdrawals (8.6)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get pending withdrawals
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            pending_withdrawals = await stats_service.get_pending_withdrawals()

            if not pending_withdrawals:
                await query.edit_message_text(
                    "✅ <b>No Pending Withdrawals</b>\n\nAll withdrawal requests have been processed.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "⏳ Pending Withdrawals:\n\n"

            for i, withdrawal in enumerate(pending_withdrawals, 1):
                username = withdrawal.get('username', 'N/A')
                first_name = withdrawal.get('first_name', 'Unknown')
                amount = withdrawal.get('amount', 0)
                method = withdrawal.get('withdrawal_method', 'Unknown')
                created_at = withdrawal.get('created_at', 0)
                user_id_str = str(withdrawal.get('user_id', 'N/A'))

                # Format timestamp
                from datetime import datetime
                try:
                    date_str = datetime.fromtimestamp(created_at).strftime("%Y-%m-%d %H:%M")
                except:
                    date_str = "Unknown"

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Amount: ₹{amount:.2f}\n"
                message += f"   Method: {method} | Date: {date_str}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='pending_withdrawals')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_pending_withdrawals: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading pending withdrawals. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_user_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle History Of Users Panel (8.7)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👥 History Of Users\n\nSelect the type of user activity to view:"

            # Build keyboard with all history options
            keyboard = [
                [
                    InlineKeyboardButton('🆕 Recently Joined', callback_data='history_recently_joined_1')
                ],
                [
                    InlineKeyboardButton('💰 Referral Rewards', callback_data='history_referral_rewards_1')
                ],
                [
                    InlineKeyboardButton('🎁 Claimed Gifts', callback_data='history_claimed_gifts_1')
                ],
                [
                    InlineKeyboardButton('💸 Withdrawal History', callback_data='history_withdrawal_history_1')
                ],
                [
                    InlineKeyboardButton('🔗 Custom Link Usage', callback_data='history_custom_link_usage_1')
                ],
                [
                    InlineKeyboardButton('📋 Full User Activity Log', callback_data='history_full_activity_log_1')
                ],
                [
                    InlineKeyboardButton('✅ Task Completion Tracker', callback_data='history_task_completions_1')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_history: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading user history panel. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_user_history_detail(self, update: Update, context: ContextTypes.DEFAULT_TYPE, history_type: str, page: int = 1) -> None:
        """Handle detailed user history with pagination"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get history data
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()

            history_data, total_records = await stats_service.get_user_history(history_type, page, 15)

            if not history_data:
                await query.edit_message_text(
                    f"❌ <b>No Data</b>\n\nNo {history_type.replace('_', ' ')} records found.",
                    parse_mode='HTML'
                )
                return

            # Calculate pagination
            total_pages = (total_records + 14) // 15  # Ceiling division

            # Build message based on history type
            message = await self._build_history_message(history_type, history_data, page, total_pages, total_records)

            # Build pagination keyboard
            keyboard = await self._build_history_keyboard(history_type, page, total_pages)

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_history_detail: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading history details. Please try again later.",
                parse_mode='HTML'
            )

    async def _build_history_message(self, history_type: str, data: List[Dict[str, Any]], page: int, total_pages: int, total_records: int) -> str:
        """Build history message based on type"""
        try:
            # Title mapping
            titles = {
                "recently_joined": "🆕 Recently Joined Users",
                "referral_rewards": "💰 Referral Rewards",
                "claimed_gifts": "🎁 Claimed Gifts",
                "withdrawal_history": "💸 Withdrawal History",
                "custom_link_usage": "🔗 Custom Link Usage",
                "full_activity_log": "📋 Full User Activity Log",
                "task_completions": "✅ Task Completions"
            }

            title = titles.get(history_type, "User History")
            message = f"{title}\n\n"

            # Add records based on type
            for i, record in enumerate(data, 1):
                if history_type == "recently_joined":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    join_bonus = record.get('join_bonus', 0)
                    referred_by = record.get('referred_by', 'Direct')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('created_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Bonus: ₹{join_bonus}\n"
                    message += f"   Referred by: {referred_by} | Date: {date_str}\n\n"

                elif history_type == "referral_rewards":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    earnings = record.get('referral_earnings', 0)

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Earnings: ₹{earnings:.2f}\n\n"

                elif history_type == "claimed_gifts":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    amount = record.get('amount', 0)
                    code = record.get('code', 'Unknown')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('used_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Amount: ₹{amount}\n"
                    message += f"   Code: {code} | Date: {date_str}\n\n"

                elif history_type == "withdrawal_history":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    amount = record.get('amount', 0)
                    method = record.get('withdrawal_method', 'Unknown')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('completed_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Amount: ₹{amount}\n"
                    message += f"   Method: {method} | Date: {date_str}\n\n"

                elif history_type == "custom_link_usage":
                    link_id = record.get('link_id', 'Unknown')
                    usage_count = record.get('usage_count', 0)
                    total_rewards = record.get('total_rewards', 0)

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('last_used', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. Link: {link_id}\n"
                    message += f"   Usage: {usage_count} | Rewards: ₹{total_rewards}\n"
                    message += f"   Last Used: {date_str}\n\n"

                elif history_type == "full_activity_log":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    action = record.get('action', 'Unknown')
                    details = record.get('details', '')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('timestamp', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Action: {action}\n"
                    message += f"   Details: {details} | Date: {date_str}\n\n"

                elif history_type == "task_completions":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    task_name = record.get('task_name', 'Unknown')
                    reward = record.get('reward_earned', 0)

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('approved_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Task: {task_name}\n"
                    message += f"   Reward: ₹{reward} | Date: {date_str}\n\n"

            # Add pagination info
            message += f"\n📄 Page {page} of {total_pages} | Total Records: {total_records}"

            return message

        except Exception as e:
            logger.error(f"Error building history message: {e}")
            return "❌ Error building message"

    async def _build_history_keyboard(self, history_type: str, page: int, total_pages: int) -> List[List[InlineKeyboardButton]]:
        """Build pagination keyboard for history"""
        try:
            keyboard = []

            # Pagination row
            pagination_row = []

            # Previous button
            if page > 1:
                pagination_row.append(
                    InlineKeyboardButton('◀️ Previous', callback_data=f'history_{history_type}_{page-1}')
                )

            # Next button
            if page < total_pages:
                pagination_row.append(
                    InlineKeyboardButton('▶️ Next', callback_data=f'history_{history_type}_{page+1}')
                )

            if pagination_row:
                keyboard.append(pagination_row)

            # Action buttons row
            action_row = [
                InlineKeyboardButton('🔄 Refresh', callback_data=f'history_{history_type}_{page}')
            ]
            keyboard.append(action_row)

            # Back button
            keyboard.append([
                InlineKeyboardButton('↩️ Go Back', callback_data='user_history')
            ])

            return keyboard

        except Exception as e:
            logger.error(f"Error building history keyboard: {e}")
            return [[InlineKeyboardButton('↩️ Go Back', callback_data='user_history')]]

    # ==================== GIFT CODE MANAGEMENT SYSTEM ====================

    async def handle_manage_gift_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Gift Code Management Panel (9.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get gift code statistics
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()
            stats = await gift_service.get_comprehensive_statistics()

            if not stats:
                stats = {
                    'total_codes': 0, 'live_codes': 0, 'used_codes': 0,
                    'total_claims': 0, 'total_amount': 0,
                    'total_link_codes': 0, 'total_link_claims': 0, 'total_link_amount': 0
                }

            # Build message
            message = "⚙️ Hello, Welcome To Code Settings.\n\n"
            message += f"▫️ Total Codes: {stats['total_codes']} (Live: {stats['live_codes']} & Used: {stats['used_codes']})\n\n"
            message += f"▫️ Total Claims: {stats['total_claims']}\n"
            message += f"▫️ Total Balance Added: ₹{stats['total_amount']:.2f}\n\n"
            message += f"▫️ Total Codes Created By Link: {stats['total_link_codes']}\n"
            message += f"▫️ Total Link Claims: {stats['total_link_claims']}\n"
            message += f"▫️ Total Link Amounts: ₹{stats['total_link_amount']:.2f}"

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton('➕ Add Redeem Code', callback_data='add_redeem_code')
                ],
                [
                    InlineKeyboardButton('🟢 Live Codes', callback_data='live_codes')
                ],
                [
                    InlineKeyboardButton('🔴 Used Codes', callback_data='used_codes')
                ],
                [
                    InlineKeyboardButton('🔗 Link Based Redeem', callback_data='link_based_redeem')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_gift_codes: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading gift code management. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_add_redeem_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Add Redeem Code workflow (9.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add Redeem Code</b>\n\n"
            message += "Send the code details as mentioned below:\n\n"
            message += "min-max,totalUsers,code (e.g., 10-50,80,PROMO)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_redeem_code')

        except Exception as e:
            logger.error(f"Error in handle_add_redeem_code: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_live_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Live Codes Display (9.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get live codes
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()
            live_codes = await gift_service.get_live_codes_detailed()

            if not live_codes:
                await query.edit_message_text(
                    "🟢 <b>Live Codes</b>\n\nNo active codes found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "🟢 <b>Live Codes</b>\n\n"

            for i, code in enumerate(live_codes, 1):
                code_name = code.get('code', 'Unknown')
                min_amount = code.get('min_amount', 0)
                max_amount = code.get('max_amount', 0)
                usage_limit = code.get('usage_limit', 0)
                usage_count = code.get('usage_count', 0)
                remaining = usage_limit - usage_count

                from datetime import datetime
                try:
                    date_str = datetime.fromtimestamp(code.get('created_at', 0)).strftime("%Y-%m-%d")
                except:
                    date_str = "Unknown"

                message += f"{i}. <b>{code_name}</b>\n"
                message += f"   Amount: ₹{min_amount}-₹{max_amount}\n"
                message += f"   Usage: {usage_count}/{usage_limit} (Remaining: {remaining})\n"
                message += f"   Created: {date_str}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='live_codes')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_live_codes: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading live codes. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_used_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Used Codes Display (9.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get used codes
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()
            used_codes = await gift_service.get_used_codes_detailed()

            if not used_codes:
                await query.edit_message_text(
                    "🔴 <b>Used Codes</b>\n\nNo completed codes found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "🔴 <b>Used Codes</b>\n\n"

            for i, code in enumerate(used_codes, 1):
                code_name = code.get('code', 'Unknown')
                min_amount = code.get('min_amount', 0)
                max_amount = code.get('max_amount', 0)
                usage_limit = code.get('usage_limit', 0)
                usage_count = code.get('usage_count', 0)

                # Calculate total distributed (approximate)
                avg_amount = (min_amount + max_amount) / 2
                total_distributed = avg_amount * usage_count

                message += f"🎁 Redeem Code: <b>{code_name}</b>\n"
                message += f"▫️ Total Users: {usage_limit}\n"
                message += f"▫️ Claim Amount: ₹{min_amount}-₹{max_amount}\n\n"
                message += f"▫️ Claimed: {usage_count}\n"
                message += f"▫️ Balance Added: ₹{total_distributed:.2f}\n\n"
                message += f"🔴 Status: Stopped\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='used_codes')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_used_codes: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading used codes. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_link_based_redeem(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Link Based Redeem System (9.4)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current link settings from session or default
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            link_settings = await session_handlers.get_user_session(user_id) or {}

            amount_type = link_settings.get('amount_type', None)
            amount_value = link_settings.get('amount_value', None)
            user_limit = link_settings.get('user_limit', None)

            message = "🔗 <b>Link Based Redeem</b>\n\n"
            message += "Configure your link-based redemption settings:\n\n"

            # Build keyboard with current selections
            keyboard = []

            # Amount type selection
            if amount_type == 'fixed':
                keyboard.append([InlineKeyboardButton('💰 Fixed Amount ✅', callback_data='link_fixed_amount')])
                keyboard.append([InlineKeyboardButton('🎲 Random Amount', callback_data='link_random_amount')])
                if amount_value:
                    message += f"💰 Fixed Amount: ₹{amount_value} per user\n"
            elif amount_type == 'random':
                keyboard.append([InlineKeyboardButton('💰 Fixed Amount', callback_data='link_fixed_amount')])
                keyboard.append([InlineKeyboardButton('🎲 Random Amount ✅', callback_data='link_random_amount')])
                if amount_value:
                    message += f"🎲 Random Amount: ₹{amount_value['min']}-₹{amount_value['max']}\n"
            else:
                keyboard.append([InlineKeyboardButton('💰 Fixed Amount', callback_data='link_fixed_amount')])
                keyboard.append([InlineKeyboardButton('🎲 Random Amount', callback_data='link_random_amount')])

            # User limit
            keyboard.append([InlineKeyboardButton('👥 Total Users Limit', callback_data='link_user_limit')])
            if user_limit is not None:
                if user_limit == -1:
                    message += f"👥 User Limit: Unlimited\n"
                else:
                    message += f"👥 User Limit: {user_limit} users\n"

            # Generate link button (only if amount type is set)
            if amount_type:
                keyboard.append([InlineKeyboardButton('🔄 Generate Link', callback_data='generate_redeem_link')])

            keyboard.append([InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')])

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_link_based_redeem: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading link-based redeem. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_link_fixed_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Fixed Amount workflow (9.4.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "💰 <b>Fixed Amount</b>\n\n"
            message += "Send the new fixed amount to set:\n\n"
            message += "(e.g., 3 for ₹3 per user)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'link_fixed_amount')

        except Exception as e:
            logger.error(f"Error in handle_link_fixed_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_link_random_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Random Amount workflow (9.4.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🎲 <b>Random Amount</b>\n\n"
            message += "Send the new random amount range to set:\n\n"
            message += "(e.g., 1-5 for ₹1–₹5 range)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'link_random_amount')

        except Exception as e:
            logger.error(f"Error in handle_link_random_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_link_user_limit(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Total Users workflow (9.4.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👥 <b>Total Users Limit</b>\n\n"
            message += "Send total number of users who can claim:\n\n"
            message += "(Send 'unlimited' for no limit)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'link_user_limit')

        except Exception as e:
            logger.error(f"Error in handle_link_user_limit: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_generate_redeem_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Generate Link workflow (9.4.4)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get link settings from session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            link_settings = await session_handlers.get_user_session(user_id) or {}

            amount_type = link_settings.get('amount_type')
            amount_value = link_settings.get('amount_value')
            user_limit = link_settings.get('user_limit', -1)

            # Validate that amount type is selected
            if not amount_type or not amount_value:
                await query.edit_message_text(
                    "❌ <b>Configuration Required</b>\n\nPlease set the amount type (Fixed or Random) first.",
                    parse_mode='HTML'
                )
                return

            # Generate unique code and create link
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()

            code = await gift_service.generate_link_code()
            if not code:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to generate unique code. Please try again.",
                    parse_mode='HTML'
                )
                return

            # Create link-based code in database
            success = await gift_service.create_link_based_code(
                code=code,
                amount_type=amount_type,
                amount_value=amount_value,
                user_limit=user_limit,
                created_by=user_id
            )

            if not success:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to create redeem link. Please try again.",
                    parse_mode='HTML'
                )
                return

            # Get bot username for link generation
            from config.settings import settings
            bot_username = settings.BOT_USERNAME

            # Build success message
            message = "🔗 <b>Redeem Link Generated!</b>\n\n"
            message += f"Link: https://t.me/{bot_username}?start=redeem_{code}\n\n"
            message += "Share this link with users to claim rewards!"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Generate Another', callback_data='link_based_redeem')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

            # Clear session after successful generation
            await session_handlers.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in handle_generate_redeem_link: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while generating link. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== FORCESUB CHANNELS MANAGEMENT SYSTEM ====================

    async def handle_forcesub_channels(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle ForceSub Channels Management Panel (10.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get force subscribe statistics and channels
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService()
            message = await force_service.format_admin_channels_message()

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton('🏡 Set Main Channel', callback_data='set_main_channel')
                ],
                [
                    InlineKeyboardButton('➕ Add Channel', callback_data='add_forcesub_channel')
                ],
                [
                    InlineKeyboardButton('➖ Remove Channel', callback_data='remove_forcesub_channel')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_forcesub_channels: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading ForceSub channels. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_set_main_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Set Main Channel workflow (10.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🏡 <b>Set Main Channel</b>\n\n"
            message += "Send the main channel username or invite link:\n\n"
            message += "(e.g., @channelname or https://t.me/+invitelink)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_main_channel')

        except Exception as e:
            logger.error(f"Error in handle_set_main_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_add_forcesub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Add Channel workflow (10.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add Channel</b>\n\n"
            message += "Send the channel details:\n\n"
            message += "For Public: @channelname\n"
            message += "For Private: https://t.me/+invitelink\n\n"
            message += "Send /cancel to cancel."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_forcesub_channel')

        except Exception as e:
            logger.error(f"Error in handle_add_forcesub_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_forcesub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Remove Channel workflow (10.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get channels for removal
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService()
            channels_for_removal = await force_service.get_channels_for_removal()

            if not channels_for_removal:
                await query.edit_message_text(
                    "➖ <b>Remove Channel</b>\n\n❌ No channels configured to remove.",
                    parse_mode='HTML'
                )
                return

            # Build message with numbered channels
            message = "➖ <b>Remove Channel</b>\n\n"
            message += "Select channel number to remove:\n\n"

            for channel_info in channels_for_removal:
                message += f"{channel_info['number']}. {channel_info['display_name']}\n"

            message += "\nSend the number or /cancel to cancel."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_forcesub_channel')

        except Exception as e:
            logger.error(f"Error in handle_remove_forcesub_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== GIFT BROADCAST SYSTEM ====================

    async def handle_gift_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Gift Broadcast Panel (11.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🎁 <b>Gift Broadcast - Channel Selection</b>\n\n"
            message += "Create a channel-based reward campaign where users join a channel to receive rewards.\n\n"
            message += "📋 <b>How it works:</b>\n"
            message += "1️⃣ Select a channel for the campaign\n"
            message += "2️⃣ Set the reward amount per user\n"
            message += "3️⃣ Bot broadcasts the campaign to all users\n"
            message += "4️⃣ Users join the channel and claim rewards\n\n"
            message += "🔧 <b>Easy Channel Setup:</b>\n"
            message += "1️⃣ First, add this bot as an administrator to your target channel\n"
            message += "2️⃣ Then, forward any message from that channel to this bot\n"
            message += "3️⃣ The bot will automatically detect and configure the channel\n\n"
            message += "💡 <b>This works for both public and private channels!</b>\n\n"
            message += "⚠️ <b>Requirements:</b>\n"
            message += "✅ The bot must be an admin in the target channel\n"
            message += "✅ You must have access to forward messages from the channel\n\n"
            message += "📤 <b>Please forward a message from your target channel now:</b>"

            keyboard = [
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

            # Set user session for forwarded message input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'gift_broadcast_forward')

        except Exception as e:
            logger.error(f"Error in handle_gift_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Gift Broadcast. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== BOT MAINTENANCE SYSTEM ====================

    async def handle_bot_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Bot Maintenance Panel (12.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get maintenance status and format message
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            message = await maintenance_service.format_maintenance_status_message()

            # Get current status for button labels
            status = await maintenance_service.get_maintenance_status()
            general_toggle = "ON⬆️" if status["general_maintenance"] else "OFF⬇️"

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton(f'🔧 General Maintenance {general_toggle}', callback_data='toggle_general_maintenance')
                ],
                [
                    InlineKeyboardButton('💵 Withdrawal Maintenance', callback_data='withdrawal_maintenance')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_bot_maintenance: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Bot Maintenance. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_toggle_general_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle General Maintenance Toggle (12.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle general maintenance
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            success, message = await maintenance_service.toggle_general_maintenance(user_id)

            if success:
                # Refresh the maintenance panel
                await self.handle_bot_maintenance(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_general_maintenance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_withdrawal_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Withdrawal Maintenance Panel (12.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get withdrawal maintenance status and format message
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            message = await maintenance_service.format_withdrawal_maintenance_message()

            # Get current status for button labels
            status = await maintenance_service.get_maintenance_status()
            bank_toggle = "ON⬆️" if status["bank_withdrawal_maintenance"] else "OFF⬇️"
            usdt_toggle = "ON⬆️" if status["usdt_withdrawal_maintenance"] else "OFF⬇️"

            # Build keyboard with horizontal layout
            keyboard = [
                [
                    InlineKeyboardButton(f'🏦 Bank {bank_toggle}', callback_data='toggle_bank_maintenance')
                ],
                [
                    InlineKeyboardButton(f'💲 USDT {usdt_toggle}', callback_data='toggle_usdt_maintenance')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_maintenance')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_withdrawal_maintenance: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Withdrawal Maintenance. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_toggle_bank_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Bank Withdrawal Maintenance Toggle (12.2.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle bank withdrawal maintenance
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            success, message = await maintenance_service.toggle_bank_withdrawal_maintenance(user_id)

            if success:
                # Refresh the withdrawal maintenance panel
                await self.handle_withdrawal_maintenance(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_bank_maintenance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_usdt_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle USDT Withdrawal Maintenance Toggle (12.2.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle USDT withdrawal maintenance
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            success, message = await maintenance_service.toggle_usdt_withdrawal_maintenance(user_id)

            if success:
                # Refresh the withdrawal maintenance panel
                await self.handle_withdrawal_maintenance(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_usdt_maintenance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== CUSTOM REFERRAL LINK SYSTEM ====================

    async def handle_custom_referral_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Custom Referral Link Panel (13.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get custom referral status and format message
            from services.custom_referral_service import CustomReferralService
            referral_service = CustomReferralService()
            message = await referral_service.format_admin_panel_message()

            # Get current status for button labels
            stats = await referral_service.get_admin_statistics()
            toggle_text = "❌ Turn Off" if stats["system_enabled"] else "✅ Turn On"

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton(toggle_text, callback_data='toggle_custom_referral')
                ],
                [
                    InlineKeyboardButton('🗑 Delete Invite Link', callback_data='delete_invite_link')
                ],
                [
                    InlineKeyboardButton('📋 Copy My Link', callback_data='copy_admin_link')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_link: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Custom Referral Link. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_toggle_custom_referral(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Custom Referral Toggle (13.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle custom referral system
            from services.custom_referral_service import CustomReferralService
            referral_service = CustomReferralService()
            success, message = await referral_service.toggle_custom_referral_system(user_id)

            if success:
                # Refresh the custom referral panel
                await self.handle_custom_referral_link(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_custom_referral: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_invite_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Delete Invite Link workflow (13.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🗑 <b>Delete Invite Link</b>\n\n"
            message += "Send the custom parameter or user ID of the link to delete:\n\n"
            message += "Examples:\n"
            message += "• from_whatsapp (parameter)\n"
            message += "• ********* (user ID)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'delete_invite_link')

        except Exception as e:
            logger.error(f"Error in handle_delete_invite_link: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_copy_admin_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Copy My Link (13.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get admin's custom link
            from services.custom_referral_service import CustomReferralService
            referral_service = CustomReferralService()

            # Check if admin has a custom link
            has_link = await referral_service.user_has_custom_link(user_id)

            if has_link:
                # Get the link
                custom_link = await referral_service.get_user_custom_link(user_id)
                if custom_link:
                    message = f"📋 <b>Your Custom Referral Link</b>\n\n"
                    message += f"🔗 Link: {custom_link}\n\n"
                    message += f"You can copy and share this link to earn referral rewards!"
                else:
                    message = "❌ Error retrieving your custom link."
            else:
                # Admin doesn't have a custom link
                message = "📋 <b>No Custom Link Found</b>\n\n"
                message += "You don't have a custom referral link yet.\n\n"
                message += "💡 Use /customref create [parameter] to create one!"

            keyboard = [
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='custom_referral_link')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_copy_admin_link: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while retrieving your link. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_custom_referral_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /customref command"""
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await update.message.reply_text(ADMIN_ACCESS_DENIED, parse_mode='HTML')
                return

            # Parse command arguments
            text = update.message.text
            parts = text.split(' ')

            if len(parts) < 2:
                await self._show_custom_referral_help(update)
                return

            command = parts[1]
            parameters = parts[2:] if len(parts) > 2 else []

            # Route to appropriate handler
            if command == 'list':
                await self._handle_custom_referral_list(update)
            elif command == 'create' and len(parameters) >= 2:
                await self._handle_custom_referral_create(update, parameters[0], parameters[1])
            elif command == 'edit' and len(parameters) >= 2:
                await self._handle_custom_referral_edit(update, parameters[0], parameters[1])
            elif command == 'delete' and len(parameters) >= 1:
                await self._handle_custom_referral_delete(update, parameters[0])
            elif command == 'view' and len(parameters) >= 1:
                await self._handle_custom_referral_view(update, parameters[0])
            else:
                await self._show_custom_referral_help(update)

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_command: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== USER BONUS MANAGEMENT PANEL ====================

    async def handle_user_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle User Bonus Management Panel"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current admin settings
            admin_settings = await self.admin_service.get_admin_settings()

            # Get current bonus settings
            level_rewards_enabled = admin_settings.get('level_rewards_enabled', True)
            invite_bonus_range = admin_settings.get('per_refer_amount_range', '1-5')
            new_user_bonus_range = admin_settings.get('joining_bonus_amount_range', '4-6')

            # Build message
            message = "⚙️ Hello, Welcome To The Bonus Settings.\n\n"
            message += f"▫️ Extra Level Bonus: {'🟢 Enabled' if level_rewards_enabled else '🔴 Disabled'}\n"
            message += f"▫️ Invite Bonus: {invite_bonus_range}\n"
            message += f"▫️ New User Bonus: {new_user_bonus_range}\n"

            # Build keyboard
            level_toggle_text = "🔴 Disable Level Bonus" if level_rewards_enabled else "🟢 Enable Level Bonus"

            keyboard = [
                [
                    InlineKeyboardButton(level_toggle_text, callback_data='toggle_level_bonus')
                ],
                [
                    InlineKeyboardButton('👤 New User Bonus', callback_data='set_new_user_bonus')
                ],
                [
                    InlineKeyboardButton('🎯 Invite Bonus', callback_data='set_invite_bonus')
                ],
                [
                    InlineKeyboardButton('🏆 Level Rewards Configuration', callback_data='configure_level_rewards')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_level_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle level bonus system on/off"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Get current status
            admin_settings = await self.admin_service.get_admin_settings()
            current_status = admin_settings.get('level_rewards_enabled', True)
            new_status = not current_status

            # Update database
            success = await self.admin_service.update_admin_setting('level_rewards_enabled', new_status)

            if success:
                status_text = "🟢 Level Bonus has been ENABLED" if new_status else "🔴 Level Bonus has been DISABLED"
                await query.answer(status_text, show_alert=True)

                # Refresh the bonus panel
                await self.handle_user_bonus(update, context)
            else:
                await query.answer("❌ Failed to update level bonus status.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_toggle_level_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_new_user_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set new user bonus range"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👤 <b>New User Bonus Range</b>\n\n"
            message += "Send the new user bonus range (e.g., 40-51):\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_new_user_bonus')

        except Exception as e:
            logger.error(f"Error in handle_set_new_user_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_invite_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set invite bonus range"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🎯 <b>Invite Bonus Range</b>\n\n"
            message += "Send the invite bonus range (e.g., 1-3):\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_invite_bonus')

        except Exception as e:
            logger.error(f"Error in handle_set_invite_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_configure_level_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Configure level rewards system"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current level rewards configuration
            admin_settings = await self.admin_service.get_admin_settings()
            level_rewards_config = admin_settings.get('level_rewards_config', {
                'referral_requirements': [1, 5, 10, 15, 20, 25],
                'bonus_amounts': [2, 10, 15, 20, 25, 30]
            })
            level_rewards_enabled = admin_settings.get('level_rewards_enabled', True)

            # Build current configuration display
            message = "🏆 <b>Level Rewards Configuration</b>\n\n"
            message += f"Status: {'🟢 Enabled' if level_rewards_enabled else '🔴 Disabled'}\n\n"
            message += "<b>Current Configuration:</b>\n"

            referral_requirements = level_rewards_config.get('referral_requirements', [])
            bonus_amounts = level_rewards_config.get('bonus_amounts', [])

            for i, (req, bonus) in enumerate(zip(referral_requirements, bonus_amounts), 1):
                message += f"Level {i}: {req} referrals = ₹{bonus}\n"

            message += "\nSend referral requirements (comma-separated):\n"
            message += "Example: 1,5,10,15,20,25\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'configure_level_rewards')

        except Exception as e:
            logger.error(f"Error in handle_configure_level_rewards: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== USER DETAILS & SETTINGS PANEL ====================

    async def handle_user_details_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle User Details & Settings Panel - Initial prompt for user ID"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👤 <b>User Details & Settings</b>\n\n"
            message += "Send user's telegram Chat Id :-\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'user_details_get_id')

        except Exception as e:
            logger.error(f"Error in handle_user_details_settings: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_show_user_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Show user details and management options"""
        query = update.callback_query if update.callback_query else None
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                if query:
                    await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Get user details
            from services.user_service import UserService
            user_service = UserService()
            user_data = await user_service.get_user(target_user_id)

            if not user_data:
                error_msg = "❌ User not found in database."
                if query:
                    await query.edit_message_text(error_msg)
                else:
                    await update.message.reply_text(error_msg)
                return

            # Get additional user information
            username = user_data.get('username', '')
            display_username = f"@{username}" if username else "User"
            referred_by = user_data.get('referred_by', 'None')
            balance = user_data.get('balance', 0)
            total_withdrawn = user_data.get('successful_withdraw', 0)
            banned = user_data.get('banned', False)

            # Get referral count
            referral_count = await user_service.get_referral_count(target_user_id)

            # Get total bonus claimed (balance + withdrawn)
            total_bonus_claimed = balance + total_withdrawn

            # Get withdrawal method details (placeholder for now)
            withdrawal_method = "Not Set"

            # Build user details message
            message = f"👤 <b>User Details</b>\n\n"
            message += f"User: {display_username}\n"
            message += f"Invited by: {referred_by}\n"
            message += f"Balance: ₹{balance}\n"
            message += f"Withdrawn: ₹{total_withdrawn}\n"
            message += f"Total Invites: {referral_count}\n"
            message += f"Current Status: ₹{total_bonus_claimed}\n"
            message += f"Bank/USDT: {withdrawal_method}\n"

            # Build keyboard with dynamic ban/unban button
            ban_text = "✅ Unban User" if banned else "⚠️ Ban User"
            ban_callback = f"unban_user_{target_user_id}" if banned else f"ban_user_{target_user_id}"

            keyboard = [
                [
                    InlineKeyboardButton(ban_text, callback_data=ban_callback)
                ],
                [
                    InlineKeyboardButton('👥 Invites Record', callback_data=f'user_invites_{target_user_id}')
                ],
                [
                    InlineKeyboardButton('💸 Withdrawal Records', callback_data=f'user_withdrawals_{target_user_id}')
                ],
                [
                    InlineKeyboardButton('➕ Add Balance', callback_data=f'add_balance_{target_user_id}'),
                    InlineKeyboardButton('➖ Remove Balance', callback_data=f'remove_balance_{target_user_id}')
                ],
                [
                    InlineKeyboardButton('📋 Task Records', callback_data=f'user_tasks_{target_user_id}')
                ],
                [
                    InlineKeyboardButton('📨 Send Message', callback_data=f'send_message_{target_user_id}')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            if query:
                await query.edit_message_text(
                    text=message,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    text=message,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_show_user_details: {e}")
            error_msg = "❌ Something went wrong. Please try again later."
            if query:
                await query.answer(error_msg, show_alert=True)
            else:
                await update.message.reply_text(error_msg)

    async def handle_user_ban_toggle(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int, ban_action: bool) -> None:
        """Ban or unban a user"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Update user ban status
            from services.user_service import UserService
            user_service = UserService()
            success = await user_service.ban_user(target_user_id, ban_action)

            if success:
                action_text = "banned" if ban_action else "unbanned"
                await query.answer(f"✅ User has been {action_text}.", show_alert=True)

                # Refresh user details
                await self.handle_show_user_details(update, context, target_user_id)
            else:
                await query.answer("❌ Failed to update user status.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_user_ban_toggle: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_user_invites_record(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int, page: int = 1) -> None:
        """Show user's invites record with pagination"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get user's referrals
            from services.user_service import UserService
            user_service = UserService()
            referrals = await user_service.get_user_referrals(target_user_id)

            # Pagination settings
            per_page = 10
            total_referrals = len(referrals)
            total_pages = max(1, (total_referrals + per_page - 1) // per_page)
            page = max(1, min(page, total_pages))

            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            page_referrals = referrals[start_idx:end_idx]

            # Build message
            message = f"👥 <b>Invites Record</b>\n\n"

            if not page_referrals:
                message += "⚠️ User has no referrals yet."
            else:
                for i, referral in enumerate(page_referrals, start_idx + 1):
                    username = referral.get('username', '')
                    display_name = f"@{username}" if username else "User"
                    message += f"{i:02d}. {referral['user_id']} [{display_name}]\n"

                message += f"\nPage {page} of {total_pages}"

            # Build keyboard
            keyboard = []

            # Navigation buttons
            nav_buttons = []
            if page > 1:
                nav_buttons.append(InlineKeyboardButton('⬅️ Previous', callback_data=f'user_invites_{target_user_id}_{page-1}'))
            if page < total_pages:
                nav_buttons.append(InlineKeyboardButton('➡️ Next', callback_data=f'user_invites_{target_user_id}_{page+1}'))

            if nav_buttons:
                keyboard.append(nav_buttons)

            keyboard.append([InlineKeyboardButton('↩️ Go Back', callback_data=f'show_user_{target_user_id}')])

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_invites_record: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_user_balance_operation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int, operation: str) -> None:
        """Handle add/remove balance operations"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            operation_text = "add" if operation == "add" else "remove"
            message = f"💰 <b>{operation_text.title()} Balance</b>\n\n"
            message += f"Enter amount to {operation_text} {'to' if operation == 'add' else 'from'} user's balance:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, f'{operation}_user_balance', {'target_user_id': target_user_id})

        except Exception as e:
            logger.error(f"Error in handle_user_balance_operation: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_send_user_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Send message to user"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📨 <b>Send Message to User</b>\n\n"
            message += "Enter the message to send to user :-\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'send_user_message', {'target_user_id': target_user_id})

        except Exception as e:
            logger.error(f"Error in handle_send_user_message: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_user_task_records(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Show user's task completion records"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get user's task submissions
            from config.database import get_collection, COLLECTIONS
            collection = await get_collection(COLLECTIONS['task_submissions'])
            task_submissions = await collection.find({"user_id": target_user_id}).to_list(length=None)

            # Build message
            message = f"📋 <b>Task Records</b>\n\n"

            if not task_submissions:
                message += "⚠️ User has not completed any tasks yet."
            else:
                for submission in task_submissions:
                    task_name = submission.get('task_name', 'Unknown Task')
                    earned_amount = submission.get('earned_amount', 0)
                    message += f"• {task_name}: ₹{earned_amount}\n"

            keyboard = [
                [InlineKeyboardButton('↩️ Go Back', callback_data=f'show_user_{target_user_id}')]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_task_records: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_manage_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle manage withdrawals menu"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get withdrawal settings
            admin_settings = await self.admin_service.get_admin_settings()
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})
            withdrawal_enabled = withdrawal_settings.get('enabled', True)

            # Create withdrawal management message
            message = "💰 <b>Withdrawal Management</b>\n\n"
            message += f"📊 <b>Current Status:</b>\n"
            message += f"• Withdrawal Status: {'🟢 Enabled' if withdrawal_enabled else '🔴 Disabled'}\n"
            message += f"• Tax Type: {withdrawal_settings.get('tax_type', 'none').title()}\n"

            if withdrawal_settings.get('tax_type') == 'percentage':
                message += f"• Tax Rate: {withdrawal_settings.get('tax_percentage', 0)}%\n"
            elif withdrawal_settings.get('tax_type') == 'fixed':
                message += f"• Tax Amount: ₹{withdrawal_settings.get('tax_amount', 0)}\n"

            message += f"\n🔧 <b>Select an option to manage withdrawals:</b>"

            # Create withdrawal management keyboard
            keyboard = [
                [
                    InlineKeyboardButton(
                        '🔴 Disable Withdrawal' if withdrawal_enabled else '🟢 Enable Withdrawal',
                        callback_data='toggle_withdrawal_status'
                    )
                ],
                [
                    InlineKeyboardButton('📊 Set Percent Tax', callback_data='set_percent_tax'),
                    InlineKeyboardButton('💰 Set Fixed Tax', callback_data='set_fixed_tax')
                ],
                [
                    InlineKeyboardButton('⚠️ Disable One BANK/USDT One Time', callback_data='toggle_unique_accounts')
                ],
                [
                    InlineKeyboardButton('🚫 Ban/Unban Users Withdrawal', callback_data='ban_user_withdrawal'),
                    InlineKeyboardButton('✏️ Change Cashout Information', callback_data='change_user_cashout')
                ],
                [
                    InlineKeyboardButton('❌️ Reject All Withdrawals', callback_data='reject_all_withdrawals')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin')
                ]
            ]

            await query.edit_message_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_withdrawal_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle withdrawal enable/disable toggle"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get current withdrawal settings
            admin_settings = await self.admin_service.get_admin_settings()
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})
            current_status = withdrawal_settings.get('enabled', True)

            # Toggle status
            new_status = not current_status
            withdrawal_settings['enabled'] = new_status

            # Update withdrawal settings
            await self.admin_service.update_admin_setting('withdrawal_settings', withdrawal_settings)

            # Create response message
            if new_status:
                status_text = "🟢 Withdrawals have been ENABLED"
            else:
                status_text = "🔴 Withdrawals have been DISABLED"

            await query.answer(f"{status_text}", show_alert=True)

            # Refresh withdrawal management panel
            await self.handle_manage_withdrawals(update, context)

        except Exception as e:
            logger.error(f"Error in handle_toggle_withdrawal_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_percent_tax(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle setting percentage tax for withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "📊 <b>Set Percentage Tax</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Enter the percentage to deduct from withdrawals\n"
            message += "• Example: Enter '30' for 30% tax\n"
            message += "• Enter '0' to disable percentage tax\n\n"
            message += "📝 <b>Please send the percentage amount:</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_percent_tax')

        except Exception as e:
            logger.error(f"Error in handle_set_percent_tax: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_fixed_tax(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle setting fixed tax amount for withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "💰 <b>Set Fixed Tax Amount</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Enter the fixed amount to deduct from withdrawals\n"
            message += "• Example: Enter '5' for ₹5 tax per withdrawal\n"
            message += "• Enter '0' to disable fixed tax\n\n"
            message += "📝 <b>Please send the tax amount (₹):</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_fixed_tax')

        except Exception as e:
            logger.error(f"Error in handle_set_fixed_tax: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_unique_accounts(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle toggle for unique bank/USDT enforcement"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get current setting
            admin_settings = await self.admin_service.get_admin_settings()
            current_status = admin_settings.get('unique_bank_usdt_enforcement', False)

            # Toggle status
            new_status = not current_status
            await self.admin_service.update_admin_setting('unique_bank_usdt_enforcement', new_status)

            # Create response message
            if new_status:
                status_text = "🔒 Unique BANK/USDT enforcement ENABLED"
                detail_text = "Users cannot reuse bank/USDT details across accounts"
            else:
                status_text = "🔓 Unique BANK/USDT enforcement DISABLED"
                detail_text = "Users can reuse bank/USDT details"

            await query.answer(f"{status_text}", show_alert=True)

            # Refresh withdrawal management panel
            await self.handle_manage_withdrawals(update, context)

        except Exception as e:
            logger.error(f"Error in handle_toggle_unique_accounts: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_ban_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle ban/unban user withdrawal functionality"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "🚫 <b>Ban/Unban User Withdrawal</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Send user ID to ban/unban their withdrawal access\n"
            message += "• If user is currently banned, they will be unbanned\n"
            message += "• If user is not banned, they will be banned\n\n"
            message += "📝 <b>Please send the user ID:</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'ban_user_withdrawal')

        except Exception as e:
            logger.error(f"Error in handle_ban_user_withdrawal: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_change_user_cashout(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle changing user cashout information"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "✏️ <b>Change User Cashout Information</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Send user ID to modify their bank/USDT details\n"
            message += "• This will reset their withdrawal method setup\n"
            message += "• User will need to re-setup their withdrawal method\n\n"
            message += "📝 <b>Please send the user ID:</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'change_user_cashout')

        except Exception as e:
            logger.error(f"Error in handle_change_user_cashout: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_reject_all_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle rejecting all pending withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get pending withdrawals count
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()
            pending_withdrawals = await withdrawal_service.get_pending_withdrawals()

            if not pending_withdrawals:
                await query.answer("ℹ️ No pending withdrawals to reject.", show_alert=True)
                return

            message = f"❌️ <b>Reject All Withdrawals</b>\n\n"
            message += f"⚠️ <b>Warning:</b> This action will reject ALL pending withdrawals!\n\n"
            message += f"📊 <b>Current Status:</b>\n"
            message += f"• Pending Withdrawals: {len(pending_withdrawals)}\n"
            message += f"• Total Amount: ₹{sum(w.get('amount', 0) for w in pending_withdrawals)}\n\n"
            message += f"🔴 <b>Are you sure you want to reject all pending withdrawals?</b>"

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Yes, Reject All', callback_data='confirm_reject_all_withdrawals'),
                    InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')
                ]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_reject_all_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_confirm_reject_all_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle confirmation of rejecting all pending withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            # Get all pending withdrawals
            pending_withdrawals = await withdrawal_service.get_pending_withdrawals()

            if not pending_withdrawals:
                await query.answer("ℹ️ No pending withdrawals found.", show_alert=True)
                await self.handle_manage_withdrawals(update, context)
                return

            rejected_count = 0
            total_amount = 0

            # Reject all pending withdrawals
            for withdrawal in pending_withdrawals:
                try:
                    withdrawal_id = withdrawal.get('_id')
                    amount = withdrawal.get('amount', 0)
                    user_id_withdrawal = withdrawal.get('user_id')

                    # Reject the withdrawal
                    success = await withdrawal_service.reject_withdrawal(withdrawal_id, settings.ADMIN_ID)

                    if success:
                        rejected_count += 1
                        total_amount += amount

                        # Send notification to user
                        try:
                            from telegram import Bot
                            bot = Bot(token=settings.BOT_TOKEN)
                            await bot.send_message(
                                chat_id=user_id_withdrawal,
                                text=f"❌ <b>Withdrawal Rejected</b>\n\n"
                                     f"Your withdrawal request of ₹{amount} has been rejected by admin.\n"
                                     f"The amount has been returned to your balance.",
                                parse_mode='HTML'
                            )
                        except Exception as notify_error:
                            logger.error(f"Error notifying user {user_id_withdrawal}: {notify_error}")

                except Exception as withdrawal_error:
                    logger.error(f"Error rejecting withdrawal {withdrawal.get('_id')}: {withdrawal_error}")

            # Create success message
            message = f"✅ <b>Bulk Rejection Complete</b>\n\n"
            message += f"📊 <b>Results:</b>\n"
            message += f"• Rejected Withdrawals: {rejected_count}\n"
            message += f"• Total Amount Returned: ₹{total_amount}\n"
            message += f"• Users Notified: {rejected_count}\n\n"
            message += f"💰 All amounts have been returned to user balances."

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            await query.answer(f"✅ Successfully rejected {rejected_count} withdrawals!", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_confirm_reject_all_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== ORIGINAL ADMIN FUNCTIONALITY WITH ENHANCEMENTS ====================
    # All original admin functions are preserved with improved functionality

    async def handle_admin_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle admin callback - redirect to main admin command"""
        await self.handle_admin_command(update, context)

    # ==================== ESSENTIAL ORIGINAL ADMIN HANDLERS ====================

    async def handle_maintenance_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle maintenance status toggle"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get current maintenance status
            admin_settings = await self.admin_service.get_admin_settings()
            current_status = admin_settings.get('maintenance_status', 'Off')

            # Toggle status
            new_status = 'On' if current_status == 'Off' else 'Off'

            # Update maintenance status
            await self.admin_service.update_admin_setting('maintenance_status', new_status)

            # Create response message
            status_emoji = "🔧" if new_status == 'On' else "✅"
            message = f"⚙️ <b>Maintenance Status Updated</b>\n\n"
            message += f"{status_emoji} <b>Status:</b> {new_status}\n\n"

            if new_status == 'On':
                message += "🔧 <b>Maintenance Mode Activated</b>\n"
                message += "• Bot is now in maintenance mode\n"
                message += "• Only admins can use the bot\n"
                message += "• Regular users will see maintenance message"
            else:
                message += "✅ <b>Normal Operation Restored</b>\n"
                message += "• Bot is now fully operational\n"
                message += "• All users can access the bot\n"
                message += "• All features are available"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_maintenance_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_gift_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast gift button callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for gift code input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_gift_code')

            message = "🎁 <b>Broadcast Gift Code</b>\n\n"
            message += "Please enter the gift code you want to broadcast to all users.\n\n"
            message += "💡 <b>Note:</b> This will send a gift code message to all bot users.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_gift_button: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast text callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for broadcast message input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_message')

            message = "📢 <b>Broadcast Message</b>\n\n"
            message += "Send the message you want to broadcast to all users.\n\n"
            message += "📝 <b>Supported Content:</b>\n"
            message += "• 📄 <b>Text</b> - Plain text with HTML formatting\n"
            message += "• 📸 <b>Photo</b> - Images with optional captions\n"
            message += "• 🎥 <b>Video</b> - Video files with optional captions\n"
            message += "• 📎 <b>Document</b> - Files with optional captions\n"
            message += "• 🎵 <b>Audio</b> - Audio files with optional captions\n\n"
            message += "💡 <b>Text Formatting:</b>\n"
            message += "• <b>Bold</b>, <i>Italic</i>, <code>Code</code>\n"
            message += "• Links and emojis are supported\n\n"
            message += "📤 Simply send your content (text, photo, video, etc.) and it will be broadcast to all users.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_text: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_view_force_sub_channels(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view force subscription channels"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get force subscription channels
            admin_settings = await self.admin_service.get_admin_settings()
            force_channels = admin_settings.get('force_sub_channels', [])

            message = "📋 <b>Force Subscription Channels</b>\n\n"

            if force_channels:
                message += f"📊 <b>Total Channels:</b> {len(force_channels)}\n\n"
                for i, channel in enumerate(force_channels, 1):
                    channel_username = channel.get('username', 'Unknown')
                    channel_title = channel.get('title', 'Unknown')
                    message += f"{i}. <b>@{channel_username}</b>\n"
                    message += f"   📝 {channel_title}\n\n"
            else:
                message += "❌ <b>No force subscription channels configured.</b>\n\n"
                message += "💡 Add channels to require users to join before using the bot."

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('➕ Add Channel', callback_data='addForceSubChannel'),
                    InlineKeyboardButton('➖ Remove Channel', callback_data='removeForceSubChannel')
                ],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_view_force_sub_channels: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_otp_api_key(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle OTP API key setting"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for OTP API key input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_otp_api_key')

            message = "🔐 <b>Set OTP Website API Key</b>\n\n"
            message += "Please enter your OTP website API key.\n\n"
            message += "💡 <b>This key is used for:</b>\n"
            message += "• OTP verification services\n"
            message += "• Phone number validation\n"
            message += "• SMS verification\n\n"
            message += "🔒 <b>Security:</b> Your API key will be stored securely.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_otp_api_key: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_per_refer_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle per refer amount setting"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for per refer amount input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_per_refer_amount')

            # Get current setting
            admin_settings = await self.admin_service.get_admin_settings()
            current_range = admin_settings.get('per_refer_amount_range', '1-3')

            message = "🧑‍🤝‍🧑 <b>Set Per Refer Amount</b>\n\n"
            message += f"💰 <b>Current Range:</b> ₹{current_range}\n\n"
            message += "Please enter the new referral amount range.\n\n"
            message += "📝 <b>Format:</b> min-max (e.g., 1-5)\n"
            message += "💡 <b>Example:</b> 2-4 (users get ₹2-4 per referral)\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_per_refer_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_joining_bonus_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle joining bonus amount setting"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for joining bonus amount input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_joining_bonus_amount')

            # Get current setting
            admin_settings = await self.admin_service.get_admin_settings()
            current_range = admin_settings.get('joining_bonus_amount_range', '49-55')

            message = "💸 <b>Set Joining Bonus Amount</b>\n\n"
            message += f"💰 <b>Current Range:</b> ₹{current_range}\n\n"
            message += "Please enter the new joining bonus range.\n\n"
            message += "📝 <b>Format:</b> min-max (e.g., 50-60)\n"
            message += "💡 <b>Example:</b> 45-55 (new users get ₹45-55 bonus)\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_joining_bonus_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== PLACEHOLDER HANDLERS FOR ORIGINAL BUTTONS ====================
    # These maintain the original button functionality with enhanced user feedback

    async def handle_add_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add balance functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for add balance
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_balance')

            message = "➕ <b>Add Balance to User</b>\n\n"
            message += "Please enter the user ID to add balance to.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_add_balance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove balance functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for remove balance
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_balance')

            message = "➖ <b>Remove Balance from User</b>\n\n"
            message += "Please enter the user ID to remove balance from.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_remove_balance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle ban user functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for ban user
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'ban_user')

            message = "🚫 <b>Ban User</b>\n\n"
            message += "Please enter the user ID to ban.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "⚠️ <b>Warning:</b> Banned users cannot use the bot.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_ban_user: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_unban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle unban user functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for unban user
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'unban_user')

            message = "✔️ <b>Unban User</b>\n\n"
            message += "Please enter the user ID to unban.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "✅ <b>Note:</b> User will regain access to the bot.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_unban_user: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== ADDITIONAL ORIGINAL ADMIN HANDLERS ====================

    async def handle_check_user_record(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle check user record functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for user record check
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'check_user_record')

            message = "🔍 <b>Check User Record</b>\n\n"
            message += "Please enter the user ID to check.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "📊 <b>Information shown:</b>\n"
            message += "• User details and statistics\n"
            message += "• Balance and transaction history\n"
            message += "• Referral information\n"
            message += "• Account status\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_check_user_record: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_manage_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manage withdrawals functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "🏧 <b>Withdrawal Management</b>\n\n"
            message += "💼 <b>Manage pending withdrawal requests</b>\n\n"
            message += "📊 <b>Available Actions:</b>\n"
            message += "• View pending withdrawals\n"
            message += "• Approve withdrawal requests\n"
            message += "• Reject withdrawal requests\n"
            message += "• View withdrawal history\n\n"
            message += "💡 Select an action below:"

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Pass Withdrawal', callback_data='passUserWithdrawal'),
                    InlineKeyboardButton('❌ Fail Withdrawal', callback_data='failUserWithdrawal')
                ],
                [
                    InlineKeyboardButton('📊 View Pending', callback_data='viewPendingWithdrawals'),
                    InlineKeyboardButton('📋 Withdrawal History', callback_data='withdrawalHistory')
                ],
                [
                    InlineKeyboardButton('⚙️ Withdrawal Settings', callback_data='withdrawal_settings')
                ],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_manage_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== PLACEHOLDER HANDLERS FOR REMAINING BUTTONS ====================
    # These provide enhanced user feedback while maintaining original structure

    async def handle_main_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle main channel setting"""
        await self._handle_channel_setting(update, context, 'main_channel', 'Main Channel')

    async def handle_private_logs_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle private logs channel setting"""
        await self._handle_channel_setting(update, context, 'private_logs_channel', 'Private Logs Channel')

    async def _handle_channel_setting(self, update: Update, context: ContextTypes.DEFAULT_TYPE, setting_key: str, setting_name: str):
        """Generic handler for channel settings"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for channel setting
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, f'set_{setting_key}')

            message = f"🏘️ <b>Set {setting_name}</b>\n\n"
            message += f"Please enter the {setting_name.lower()} username.\n\n"
            message += "📝 <b>Format:</b> @channelname or channelname\n"
            message += "💡 <b>Example:</b> @mychannel\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_channel_setting for {setting_name}: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # Enhanced placeholder handlers for remaining original buttons
    async def handle_add_force_sub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add force subscription channel"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_remove_force_sub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove force subscription channel"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_pass_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle pass user withdrawal"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_fail_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle fail user withdrawal"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_manage_tasks(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manage tasks callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            all_tasks = await task_service.get_all_tasks()

            if not all_tasks:
                message = "📋 <b>Task Management</b>\n\n"
                message += "❌ No tasks found.\n\n"
                message += "Use \"➕ Add New Task\" to create your first task."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            message = "📋 <b>Task Management</b>\n\n"
            message += f"Total Tasks: {len(all_tasks)}\n\n"

            keyboard_buttons = []

            for task in all_tasks:
                status_icon = '✅' if task['status'] == 'active' else '❌'
                task_name = task['name'][:20] + '...' if len(task['name']) > 20 else task['name']

                keyboard_buttons.append([
                    InlineKeyboardButton(
                        f"{status_icon} {task_name} - ₹{task['reward_amount']}",
                        callback_data=f"editTask_{task['task_id']}"
                    )
                ])

            keyboard_buttons.extend([
                [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                [InlineKeyboardButton('📊 View Pending Submissions', callback_data='viewPendingSubmissions')],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_manage_tasks: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_add_new_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add new task callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "➕ <b>Add New Task</b>\n\n"
            message += "Let's create a new task for users to complete.\n\n"
            message += "📝 <b>Step 1:</b> Enter the task name\n"
            message += "Keep it short and descriptive (max 50 characters).\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_task_name')

        except Exception as e:
            logger.error(f"Error in handle_add_new_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task callback - show task details and editing options"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)

            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            # Get task statistics
            total_submissions = await task_service.get_task_submission_count(task_id)
            pending_submissions = await task_service.get_pending_submissions_count(task_id)
            approved_submissions = await task_service.get_approved_submissions_count(task_id)

            status_icon = '✅' if task['status'] == 'active' else '❌'
            status_text = 'Active' if task['status'] == 'active' else 'Inactive'

            message = f"✏️ <b>Edit Task</b>\n\n"
            message += f"📝 <b>Name:</b> {task['name']}\n"
            message += f"📋 <b>Description:</b>\n{task['description']}\n\n"
            message += f"💰 <b>Reward:</b> ₹{task['reward_amount']}\n"
            message += f"⚙️ <b>Status:</b> {status_icon} {status_text}\n"
            message += f"🖼️ <b>Media:</b> {'Yes' if task.get('media_url') else 'No'}\n\n"
            message += f"📊 <b>Statistics:</b>\n"
            message += f"• Total Submissions: {total_submissions}\n"
            message += f"• Pending: {pending_submissions}\n"
            message += f"• Approved: {approved_submissions}\n\n"
            message += f"🆔 <b>Task ID:</b> <code>{task_id}</code>"

            keyboard_buttons = [
                [
                    InlineKeyboardButton('📝 Edit Name', callback_data=f'editTaskName_{task_id}'),
                    InlineKeyboardButton('📋 Edit Description', callback_data=f'editTaskDesc_{task_id}')
                ],
                [
                    InlineKeyboardButton('💰 Edit Reward', callback_data=f'editTaskReward_{task_id}'),
                    InlineKeyboardButton('🖼️ Edit Media', callback_data=f'editTaskMedia_{task_id}')
                ],
                [
                    InlineKeyboardButton(
                        f'⚙️ {"Deactivate" if task["status"] == "active" else "Activate"}',
                        callback_data=f'toggleTaskStatus_{task_id}'
                    )
                ],
                [
                    InlineKeyboardButton('🗑️ Delete Task', callback_data=f'deleteTask_{task_id}')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Tasks', callback_data='manageTasks')
                ]
            ]

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            # Handle media if task has media_url
            if task.get('media_url') and task['media_url'].strip():
                try:
                    # Send photo with caption
                    await query.message.delete()
                    await context.bot.send_photo(
                        chat_id=chat_id,
                        photo=task['media_url'],
                        caption=message,
                        parse_mode='HTML',
                        reply_markup=keyboard
                    )
                except Exception as e:
                    logger.error(f"Error sending task media: {e}")
                    # Fallback to text message
                    await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_edit_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task name"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Name</b>\n\n"
            message += "Enter the new task name (max 50 characters):\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_name', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_name: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_description(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task description"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Description</b>\n\n"
            message += "Enter the new task description:\n\n"
            message += "Provide detailed instructions on what users need to do.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_description', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_description: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_reward(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task reward"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Reward</b>\n\n"
            message += "Enter the new reward amount (numbers only):\n\n"
            message += "💡 <b>Example:</b> 50 (for ₹50)\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_reward', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_reward: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_media(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task media"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Media</b>\n\n"
            message += "Send a photo/image for the task or send 'none' to remove media:\n\n"
            message += "📸 The image will be shown to users when they view the task.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_media', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_media: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_task_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle toggle task status"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            new_status = 'inactive' if task['status'] == 'active' else 'active'

            if await task_service.update_task(task_id, {'status': new_status}):
                status_text = 'activated' if new_status == 'active' else 'deactivated'
                await query.answer(f"✅ Task {status_text} successfully!", show_alert=True)

                # Refresh the edit task view
                await self.handle_edit_task(update, context, task_id)
            else:
                await query.answer("❌ Failed to update task status.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_toggle_task_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_task_confirm(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle delete task confirmation"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            total_submissions = await task_service.get_task_submission_count(task_id)

            message = f"🗑️ <b>Delete Task</b>\n\n"
            message += f"⚠️ <b>Warning:</b> This action cannot be undone!\n\n"
            message += f"📝 <b>Task:</b> {task['name']}\n"
            message += f"💰 <b>Reward:</b> ₹{task['reward_amount']}\n"
            message += f"📊 <b>Submissions:</b> {total_submissions}\n\n"
            message += f"Are you sure you want to delete this task?\n"
            message += f"All submissions will also be deleted."

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Yes, Delete', callback_data=f'confirmDeleteTask_{task_id}'),
                    InlineKeyboardButton('❌ Cancel', callback_data=f'editTask_{task_id}')
                ]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_delete_task_confirm: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle delete task"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            task_name = task['name']

            if await task_service.delete_task(task_id):
                message = f"✅ <b>Task Deleted Successfully</b>\n\n"
                message += f"📝 <b>Deleted Task:</b> {task_name}\n\n"
                message += f"The task and all its submissions have been removed."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks')],
                    [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                await query.answer("✅ Task deleted successfully!", show_alert=True)
            else:
                await query.answer("❌ Failed to delete task. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_delete_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_generate_gift_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle generate gift code"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_configure_level_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle configure level rewards"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_toggle_level_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle toggle level bonus"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_custom_referral_links(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle custom referral links"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_withdrawal_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdrawal settings"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_reset_user_account(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle reset user account details request"""
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await update.message.reply_text(ADMIN_ACCESS_DENIED, parse_mode='HTML')
                return

            # Set session for user ID input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()

            await session_handlers.set_user_session(user_id, {
                'step': 'reset_user_account_id',
                'data': {}
            })

            message = "🔄 <b>Reset User Account Details</b>\n\n"
            message += "Please enter the User ID or Username of the user whose account details you want to reset.\n\n"
            message += "📝 <b>You can enter:</b>\n"
            message += "• User ID (e.g., *********)\n"
            message += "• Username (e.g., @username)\n\n"
            message += "⚠️ <b>Warning:</b> This will clear all withdrawal method settings and account details for the user.\n\n"
            message += "Send /cancel to cancel this process."

            await update.message.reply_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_reset_user_account: {e}")
            await update.message.reply_text("❌ Error initiating account reset. Please try again.")

    # ==================== CUSTOM REFERRAL HELPER METHODS ====================

    async def _show_custom_referral_help(self, update: Update):
        """Show custom referral help (matching PHP version exactly)"""
        from models.custom_referral import CustomReferralModel

        help_text = CustomReferralModel.format_custom_referral_help_message()
        keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

        await update.message.reply_text(help_text, reply_markup=keyboard, parse_mode='HTML')

    async def _handle_custom_referral_list(self, update: Update):
        """Handle custom referral list (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()

            # Get all custom referrals
            referrals = await custom_referral_service.get_all_custom_referrals()

            # Format message
            message = CustomReferralModel.format_custom_referral_list_message(referrals)
            keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_list: {e}")
            await update.message.reply_text("❌ Error loading custom referral links.", parse_mode='HTML')

    async def _handle_custom_referral_create(self, update: Update, param: str, user_id_str: str):
        """Handle custom referral create (matching PHP version exactly)"""
        try:
            # Validate user ID
            try:
                user_id = int(user_id_str)
            except ValueError:
                await update.message.reply_text("❌ Invalid user ID. Please provide a valid number.", parse_mode='HTML')
                return

            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Create custom referral
            result = await custom_referral_service.create_custom_referral(param, user_id, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_creation_success_message(
                    result['custom_param'],
                    result['custom_link'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_create: {e}")
            await update.message.reply_text("❌ Error creating custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_edit(self, update: Update, old_param: str, new_param: str):
        """Handle custom referral edit (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Update custom referral
            result = await custom_referral_service.update_custom_referral(old_param, new_param, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_update_success_message(
                    result['old_param'],
                    result['new_param'],
                    result['custom_link'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_edit: {e}")
            await update.message.reply_text("❌ Error updating custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_delete(self, update: Update, param: str):
        """Handle custom referral delete (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Delete custom referral
            result = await custom_referral_service.delete_custom_referral(param, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_deletion_success_message(
                    result['param'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_delete: {e}")
            await update.message.reply_text("❌ Error deleting custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_view(self, update: Update, user_id_str: str):
        """Handle custom referral view (matching PHP version exactly)"""
        try:
            # Validate user ID
            try:
                user_id = int(user_id_str)
            except ValueError:
                await update.message.reply_text("❌ Invalid user ID. Please provide a valid number.", parse_mode='HTML')
                return

            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()

            # Get custom referrals for user
            referrals = await custom_referral_service.get_custom_referrals_by_user(user_id)

            if not referrals:
                await update.message.reply_text(f"❌ No custom referral links found for user {user_id}.", parse_mode='HTML')
                return

            # Format message for specific user
            message = f"🔗 <b>Custom Referral Links for User {user_id}</b>\n\n"

            for i, referral in enumerate(referrals, 1):
                param = referral.get('custom_param', 'Unknown')
                clicks = referral.get('clicks', 0)
                conversions = referral.get('referrals', 0)
                active = referral.get('active', True)

                status_emoji = "✅" if active else "❌"

                message += f"<b>{i}.</b> {status_emoji} <code>{param}</code>\n"
                message += f"   📊 {clicks} clicks, {conversions} referrals\n\n"

            keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_view: {e}")
            await update.message.reply_text("❌ Error viewing custom referral links.", parse_mode='HTML')

    async def handle_custom_referral_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE, action: str):
        """Handle custom referral callback actions (matching PHP version exactly)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not is_admin(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            if action == 'list':
                from services.custom_referral_service import CustomReferralService
                from models.custom_referral import CustomReferralModel

                custom_referral_service = CustomReferralService()

                # Get all custom referrals
                referrals = await custom_referral_service.get_all_custom_referrals()

                # Format message
                message = CustomReferralModel.format_custom_referral_list_message(referrals)
                keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
            elif action == 'help':
                from models.custom_referral import CustomReferralModel

                help_text = CustomReferralModel.format_custom_referral_help_message()
                keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

                await query.edit_message_text(help_text, reply_markup=keyboard, parse_mode='HTML')
            else:
                # Default action - show custom referral management
                await self.handle_custom_referral_link(update, context)

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_callback: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
