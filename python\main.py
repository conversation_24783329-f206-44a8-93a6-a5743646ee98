"""
Main entry point for the Python Telegram Referral Bot
Maintains identical functionality to PHP version with polling-based architecture
"""

import asyncio
import logging
import signal
import sys
from typing import Optional

from telegram import Update
from telegram.ext import (
    Application, 
    CommandH<PERSON>ler, 
    MessageHandler, 
    CallbackQueryHandler,
    filters
)
from telegram.error import TelegramError

from config.settings import settings
from config.database import db_manager
from handlers.user_handlers import UserHandlers
from handlers.admin_handlers import AdminHandlers
from handlers.callback_handlers import CallbackHandlers
from handlers.session_handlers import SessionHandlers
from utils.helpers import is_admin

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    handlers=[
        logging.FileHandler(settings.LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class TelegramBot:
    """Main bot class with polling-based architecture"""
    
    def __init__(self):
        self.application: Optional[Application] = None
        self.user_handlers = UserHandlers()
        self.admin_handlers = AdminHandlers()
        self.callback_handlers = CallbackHandlers()
        self.session_handlers = SessionHandlers()
        self._shutdown_event = asyncio.Event()
    
    async def initialize(self) -> bool:
        """Initialize bot and database connections"""
        try:
            # Connect to database
            logger.info("Connecting to MongoDB...")
            if not await db_manager.connect():
                logger.error("Failed to connect to database")
                return False
            
            # Create application
            logger.info("Initializing Telegram bot...")
            self.application = (
                Application.builder()
                .token(settings.BOT_TOKEN)
                .build()
            )
            
            # Register handlers
            await self._register_handlers()
            
            # Initialize bot info
            await self._initialize_bot_info()
            
            logger.info("Bot initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}")
            return False
    
    async def _register_handlers(self):
        """Register all message and callback handlers"""
        app = self.application
        
        # Command handlers
        app.add_handler(CommandHandler("start", self._handle_start))
        app.add_handler(CommandHandler("admin", self._handle_admin))
        app.add_handler(CommandHandler("rank", self._handle_rank))
        app.add_handler(CommandHandler("cancel", self._handle_cancel))
        app.add_handler(CommandHandler("debug", self._handle_debug))
        app.add_handler(CommandHandler("customref", self._handle_custom_ref))
        
        # Callback query handler
        app.add_handler(CallbackQueryHandler(self._handle_callback_query))
        
        # Message handler for sessions and regular messages
        app.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND, 
            self._handle_message
        ))
        
        # Media handlers for task submissions and broadcasts
        app.add_handler(MessageHandler(
            filters.PHOTO | filters.Document.ALL | filters.VIDEO | filters.AUDIO,
            self._handle_media
        ))
        
        # Error handler
        app.add_error_handler(self._handle_error)
        
        logger.info("All handlers registered successfully")
    
    async def _initialize_bot_info(self):
        """Initialize bot information and admin settings in database"""
        try:
            bot = self.application.bot
            bot_info = await bot.get_me()

            # Initialize admin service and settings
            from services.admin_service import AdminService
            admin_service = AdminService()

            # Update bot info in database
            await admin_service.update_bot_info(bot_info.username, bot_info.first_name)

            # Initialize all admin settings including level rewards
            await admin_service.initialize_all_admin_settings()

            # Clean up any stale broadcast sessions from previous runs
            cleaned_count = await admin_service.cleanup_stale_broadcast_sessions(max_age_hours=1)
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} stale broadcast sessions from previous runs")

            logger.info(f"Bot info updated: @{bot_info.username} ({bot_info.first_name})")
            logger.info("Admin settings and level rewards system initialized")

        except Exception as e:
            logger.error(f"Failed to initialize bot info and admin settings: {e}")
    
    async def _handle_start(self, update: Update, context) -> None:
        """Handle /start command"""
        try:
            # Check if update has valid user information
            if not update.effective_user or not update.message:
                logger.warning("Received start command with missing user or message information")
                return

            user_id = update.effective_user.id

            # Bot is permanently enabled - no global disable check needed

            await self.user_handlers.handle_start_command(update, context)
        except Exception as e:
            logger.error(f"Error in start handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_admin(self, update: Update, context) -> None:
        """Handle /admin command"""
        try:
            user_id = update.effective_user.id

            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                logger.debug(f"User {user_id} denied access to /admin command in main handler - not an admin")
                return

            await self.admin_handlers.handle_admin_command(update, context)
        except Exception as e:
            logger.error(f"Error in admin handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_rank(self, update: Update, context) -> None:
        """Handle /rank command"""
        try:
            user_id = update.effective_user.id
            if not is_admin(user_id):
                await update.message.reply_text(
                    "❌ <b>Access Denied</b>\n\nThis command is only available to administrators.",
                    parse_mode='HTML'
                )
                return
            
            await self.admin_handlers.handle_rank_command(update, context)
        except Exception as e:
            logger.error(f"Error in rank handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_cancel(self, update: Update, context) -> None:
        """Handle /cancel command"""
        try:
            await self.session_handlers.handle_cancel_command(update, context)
        except Exception as e:
            logger.error(f"Error in cancel handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_debug(self, update: Update, context) -> None:
        """Handle /debug command"""
        try:
            await self.user_handlers.handle_debug_command(update, context)
        except Exception as e:
            logger.error(f"Error in debug handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_custom_ref(self, update: Update, context) -> None:
        """Handle /customref command"""
        try:
            user_id = update.effective_user.id
            if not is_admin(user_id):
                return
            
            await self.admin_handlers.handle_custom_referral_command(update, context)
        except Exception as e:
            logger.error(f"Error in custom ref handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_callback_query(self, update: Update, context) -> None:
        """Handle callback queries"""
        try:
            # Check if update has valid user and callback query information
            if not update.effective_user or not update.callback_query:
                logger.warning("Received callback query with missing user or query information")
                return

            user_id = update.effective_user.id

            # Bot is permanently enabled - no global disable check needed

            await self.callback_handlers.handle_callback_query(update, context)
        except Exception as e:
            logger.error(f"Error in callback handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_message(self, update: Update, context) -> None:
        """Handle text messages (sessions and regular messages)"""
        try:
            # Check if update has valid user information
            if not update.effective_user or not update.message:
                logger.warning("Received update with missing user or message information")
                return

            user_id = update.effective_user.id

            # Bot is permanently enabled - no global disable check needed

            # Check if user has an active session
            session = await self.session_handlers.get_user_session(user_id)

            if session:
                await self.session_handlers.handle_session_message(update, context, session)
            else:
                # Handle regular messages if needed
                pass

        except Exception as e:
            logger.error(f"Error in message handler: {e}")
            await self._send_error_message(update)
    
    async def _handle_media(self, update: Update, context) -> None:
        """Handle media uploads (photos, videos, documents, audio)"""
        try:
            # Check if update has valid user information
            if not update.effective_user:
                logger.warning("Received media update with missing user information")
                return

            # Check if user has an active session for media upload
            user_id = update.effective_user.id
            session = await self.session_handlers.get_user_session(user_id)

            if session and session.get('step') in ['submit_task_screenshot', 'add_task_media', 'add_force_sub_channel', 'broadcast_message', 'edit_task_media']:
                await self.session_handlers.handle_session_message(update, context, session)
            
        except Exception as e:
            logger.error(f"Error in media handler: {e}")
            await self._send_error_message(update)



    async def _handle_error(self, update: Update, context) -> None:
        """Handle errors"""
        logger.error(f"Update {update} caused error {context.error}")
        
        if update and update.effective_chat:
            await self._send_error_message(update)
    
    async def _send_error_message(self, update: Update) -> None:
        """Send generic error message to user"""
        try:
            if update.message:
                await update.message.reply_text(
                    "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                    parse_mode='HTML'
                )
            elif update.callback_query:
                await update.callback_query.answer(
                    "❌ Something went wrong. Please try again later.",
                    show_alert=True
                )
        except Exception:
            pass  # Ignore errors when sending error messages
    
    async def start_polling(self):
        """Start the bot with polling"""
        try:
            logger.info("Starting bot polling...")
            
            # Start polling
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling(
                poll_interval=1.0,
                timeout=10,
                bootstrap_retries=-1,
                read_timeout=30,
                write_timeout=30,
                connect_timeout=30,
                pool_timeout=30
            )
            
            logger.info("Bot is now running and polling for updates...")
            
            # Wait for shutdown signal
            await self._shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"Error during polling: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Gracefully shutdown the bot"""
        logger.info("Shutting down bot...")
        
        try:
            if self.application:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
            
            # Disconnect from database
            await db_manager.disconnect()
            
            logger.info("Bot shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating shutdown...")
        self._shutdown_event.set()

async def main():
    """Main function"""
    bot = TelegramBot()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, bot.signal_handler)
    signal.signal(signal.SIGTERM, bot.signal_handler)
    
    try:
        # Initialize bot
        if not await bot.initialize():
            logger.error("Failed to initialize bot")
            sys.exit(1)
        
        # Start polling
        await bot.start_polling()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
